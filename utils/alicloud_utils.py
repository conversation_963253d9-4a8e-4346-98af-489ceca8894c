"""
阿里云工具类，提供访问阿里云API的公共函数
"""

from django.conf import settings
from aliyunsdkcore.client import AcsClient
from cryptography.fernet import Fernet

def get_client(access_key=None, access_secret=None, region_id=None):
    """获取阿里云API客户端
    
    Args:
        access_key: 访问密钥ID，如果未提供则使用配置
        access_secret: 访问密钥Secret，如果未提供则使用配置
        region_id: 区域ID，如果未提供则使用配置
    
    Returns:
        AcsClient: 阿里云SDK客户端对象
    """
    # 使用参数提供的值或默认配置

    cipher_suite = Fernet(settings.ALIYUN_SUITE_KEY.encode())
    ak = access_key or settings.ALIYUN_ACCESS_KEY_ID
    secret = cipher_suite.decrypt((access_secret or settings.ALIYUN_ACCESS_KEY_SECRET).encode()).decode()
    region = region_id or settings.ALIYUN_REGION_ID
    
    # 创建并返回客户端
    return AcsClient(
        ak,
        secret,
        region,
        timeout=300,  # 连接超时时间：300秒
        connect_timeout=60  # 建立连接超时：60秒
    ) 