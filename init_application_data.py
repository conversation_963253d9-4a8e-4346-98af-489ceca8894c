#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dba_0331.settings')
django.setup()

from django.utils import timezone
from accounts.models import User
from rds_manager.models import (
    BusinessDomain, Application,
    RDSInstance, Database
)

def create_test_data():
    """创建测试数据"""
    print("开始创建应用管理测试数据...")
    
    # 创建测试用户
    users = {}
    user_data = [
        {'username': 'tech_lead1', 'email': '<EMAIL>', 'first_name': '张三', 'is_staff': True},
        {'username': 'tech_lead2', 'email': '<EMAIL>', 'first_name': '李四', 'is_staff': True},
        {'username': 'business_owner1', 'email': '<EMAIL>', 'first_name': '王五'},
        {'username': 'business_owner2', 'email': '<EMAIL>', 'first_name': '赵六'},
        {'username': 'supervisor1', 'email': '<EMAIL>', 'first_name': '总监一', 'is_staff': True},
        {'username': 'supervisor2', 'email': '<EMAIL>', 'first_name': '总监二', 'is_staff': True},
    ]
    
    for user_info in user_data:
        user, created = User.objects.get_or_create(
            username=user_info['username'],
            defaults=user_info
        )
        if created:
            user.set_password('password123')
            user.save()
            print(f"创建用户: {user.username}")
        users[user_info['username']] = user
    
    # 创建业务域
    domains_data = [
        {
            'name': '电商平台',
            'code': 'ECOMMERCE',
            'description': '电商相关的所有应用系统',
            'team_leader_id': users['tech_lead1'].id,
            'supervisor_id': users['supervisor1'].id,
            'contact_email': '<EMAIL>',
            'contact_phone': '************'
        },
        {
            'name': '用户中心',
            'code': 'USER_CENTER',
            'description': '用户管理和认证相关系统',
            'team_leader_id': users['tech_lead2'].id,
            'supervisor_id': users['supervisor1'].id,
            'contact_email': '<EMAIL>',
            'contact_phone': '************'
        },
        {
            'name': '数据分析',
            'code': 'DATA_ANALYTICS',
            'description': '数据分析和报表系统',
            'team_leader_id': users['tech_lead1'].id,
            'supervisor_id': users['supervisor2'].id,
            'contact_email': '<EMAIL>',
            'contact_phone': '************'
        },
        {
            'name': '支付系统',
            'code': 'PAYMENT',
            'description': '支付和财务相关系统',
            'team_leader_id': users['tech_lead2'].id,
            'supervisor_id': users['supervisor2'].id,
            'contact_email': '<EMAIL>',
            'contact_phone': '************'
        }
    ]
    
    domains = {}
    for domain_data in domains_data:
        domain, created = BusinessDomain.objects.get_or_create(
            code=domain_data['code'],
            defaults=domain_data
        )
        if created:
            print(f"创建业务域: {domain.name}")
        domains[domain_data['code']] = domain
    
    # 创建应用
    applications_data = [
        {
            'name': '商城前端',
            'code': 'MALL_FRONTEND',
            'description': '电商平台的前端应用',
            'business_domain_id': domains['ECOMMERCE'].id,
            'app_type': 'web',
            'environment': 'prod',
            'version': '2.1.0',
            'tech_owner_id': users['tech_lead1'].id,
            'business_owner_id': users['business_owner1'].id,
            'contact_email': '<EMAIL>',
            'repository_url': 'https://github.com/company/mall-frontend',
            'documentation_url': 'https://docs.company.com/mall-frontend'
        },
        {
            'name': '商城API',
            'code': 'MALL_API',
            'description': '电商平台的后端API服务',
            'business_domain_id': domains['ECOMMERCE'].id,
            'app_type': 'api',
            'environment': 'prod',
            'version': '1.8.5',
            'tech_owner_id': users['tech_lead1'].id,
            'business_owner_id': users['business_owner1'].id,
            'contact_email': '<EMAIL>',
            'repository_url': 'https://github.com/company/mall-api'
        },
        {
            'name': '订单处理服务',
            'code': 'ORDER_SERVICE',
            'description': '订单处理的微服务',
            'business_domain_id': domains['ECOMMERCE'].id,
            'app_type': 'microservice',
            'environment': 'prod',
            'version': '3.2.1',
            'tech_owner_id': users['tech_lead1'].id,
            'business_owner_id': users['business_owner1'].id,
            'contact_email': '<EMAIL>'
        },
        {
            'name': '用户认证服务',
            'code': 'AUTH_SERVICE',
            'description': '用户认证和授权服务',
            'business_domain_id': domains['USER_CENTER'].id,
            'app_type': 'api',
            'environment': 'prod',
            'version': '2.0.3',
            'tech_owner_id': users['tech_lead2'].id,
            'business_owner_id': users['business_owner2'].id,
            'contact_email': '<EMAIL>'
        },
        {
            'name': '用户管理后台',
            'code': 'USER_ADMIN',
            'description': '用户管理的后台系统',
            'business_domain_id': domains['USER_CENTER'].id,
            'app_type': 'web',
            'environment': 'prod',
            'version': '1.5.2',
            'tech_owner_id': users['tech_lead2'].id,
            'business_owner_id': users['business_owner2'].id,
            'contact_email': '<EMAIL>'
        },
        {
            'name': '数据ETL服务',
            'code': 'DATA_ETL',
            'description': '数据抽取转换加载服务',
            'business_domain_id': domains['DATA_ANALYTICS'].id,
            'app_type': 'batch',
            'environment': 'prod',
            'version': '1.3.0',
            'tech_owner_id': users['tech_lead1'].id,
            'business_owner_id': users['business_owner1'].id,
            'contact_email': '<EMAIL>'
        },
        {
            'name': '报表系统',
            'code': 'REPORT_SYSTEM',
            'description': '业务报表和数据可视化系统',
            'business_domain_id': domains['DATA_ANALYTICS'].id,
            'app_type': 'web',
            'environment': 'prod',
            'version': '2.4.1',
            'tech_owner_id': users['tech_lead1'].id,
            'business_owner_id': users['business_owner1'].id,
            'contact_email': '<EMAIL>'
        },
        {
            'name': '支付网关',
            'code': 'PAYMENT_GATEWAY',
            'description': '支付网关服务',
            'business_domain_id': domains['PAYMENT'].id,
            'app_type': 'api',
            'environment': 'prod',
            'version': '1.9.0',
            'tech_owner_id': users['tech_lead2'].id,
            'business_owner_id': users['business_owner2'].id,
            'contact_email': '<EMAIL>'
        }
    ]
    
    applications = {}
    for app_data in applications_data:
        app, created = Application.objects.get_or_create(
            code=app_data['code'],
            defaults=app_data
        )
        if created:
            print(f"创建应用: {app.name}")
        applications[app_data['code']] = app
    
    # 获取现有的RDS实例和数据库（如果有的话）
    instances = list(RDSInstance.objects.all()[:3])  # 取前3个实例
    databases = list(Database.objects.all()[:10])    # 取前10个数据库
    
    # 如果没有实例和数据库，创建一些示例数据
    if not instances:
        print("没有找到RDS实例，创建示例实例...")
        instance_data = [
            {
                'instance_id': 'rm-example001',
                'instance_name': '生产数据库主库',
                'instance_type': 'Primary',
                'instance_class': 'mysql.n2.medium.1',
                'engine': 'MySQL',
                'engine_version': '8.0',
                'status': 'Running',
                'region_id': 1,
                'zone_id': 'cn-hangzhou-b',
                'connection_string': 'rm-example001.mysql.rds.aliyuncs.com',
                'port': 3306
            },
            {
                'instance_id': 'rm-example002',
                'instance_name': '生产数据库从库',
                'instance_type': 'Readonly',
                'instance_class': 'mysql.n2.medium.1',
                'engine': 'MySQL',
                'engine_version': '8.0',
                'status': 'Running',
                'region_id': 1,
                'zone_id': 'cn-hangzhou-c',
                'connection_string': 'rm-example002.mysql.rds.aliyuncs.com',
                'port': 3306
            }
        ]
        
        for inst_data in instance_data:
            instance, created = RDSInstance.objects.get_or_create(
                instance_id=inst_data['instance_id'],
                defaults=inst_data
            )
            if created:
                print(f"创建RDS实例: {instance.instance_name}")
            instances.append(instance)
    
    if not databases and instances:
        print("创建示例数据库...")
        db_data = [
            {'instance_id': instances[0].instance_id, 'name': 'mall_db', 'character_set': 'utf8mb4'},
            {'instance_id': instances[0].instance_id, 'name': 'user_db', 'character_set': 'utf8mb4'},
            {'instance_id': instances[0].instance_id, 'name': 'order_db', 'character_set': 'utf8mb4'},
            {'instance_id': instances[0].instance_id, 'name': 'payment_db', 'character_set': 'utf8mb4'},
            {'instance_id': instances[1].instance_id if len(instances) > 1 else instances[0].instance_id, 'name': 'analytics_db', 'character_set': 'utf8mb4'},
        ]
        
        for db_data_item in db_data:
            db, created = Database.objects.get_or_create(
                instance_id=db_data_item['instance_id'],
                name=db_data_item['name'],
                defaults=db_data_item
            )
            if created:
                print(f"创建数据库: {db.name}")
            databases.append(db)
    
    # 创建应用数据库关联
    if instances and databases:
        relations_data = [
            {
                'application_id': applications['MALL_FRONTEND'].id,
                'instance_id': instances[0].instance_id,
                'database_name': 'mall_db',
                'relation_type': 'primary',
                'access_type': 'read_only',
                'priority': 1,
                'created_by_id': users['tech_lead1'].id,
                'notes': '商城前端只需要读取商品和订单数据'
            },
            {
                'application_id': applications['MALL_API'].id,
                'instance_id': instances[0].instance_id,
                'database_name': 'mall_db',
                'relation_type': 'primary',
                'access_type': 'read_write',
                'priority': 1,
                'created_by_id': users['tech_lead1'].id,
                'notes': '商城API需要读写商品、订单等数据'
            },
            {
                'application_id': applications['ORDER_SERVICE'].id,
                'instance_id': instances[0].instance_id,
                'database_name': 'order_db',
                'relation_type': 'primary',
                'access_type': 'read_write',
                'priority': 1,
                'created_by_id': users['tech_lead1'].id,
                'notes': '订单服务的主数据库'
            },
            {
                'application_id': applications['AUTH_SERVICE'].id,
                'instance_id': instances[0].instance_id,
                'database_name': 'user_db',
                'relation_type': 'primary',
                'access_type': 'read_write',
                'priority': 1,
                'created_by_id': users['tech_lead2'].id,
                'notes': '用户认证服务的主数据库'
            },
            {
                'application_id': applications['PAYMENT_GATEWAY'].id,
                'instance_id': instances[0].instance_id,
                'database_name': 'payment_db',
                'relation_type': 'primary',
                'access_type': 'read_write',
                'priority': 1,
                'created_by_id': users['tech_lead2'].id,
                'notes': '支付网关的主数据库'
            }
        ]
        
        for rel_data in relations_data:
            try:
                database = Database.objects.get(
                    name=rel_data['database_name'],
                    instance_id=rel_data['instance_id']
                )
                database.app_id = rel_data['application_id']
                database.assigned_by_id = users['admin'].id
                database.assigned_at = timezone.now()
                database.save()

                app = Application.objects.get(id=rel_data['application_id'])
                print(f"分配数据库: {database.name} -> {app.name}")
            except Database.DoesNotExist:
                print(f"数据库不存在: {rel_data['database_name']}")
    
    print("测试数据创建完成！")
    print("\n统计信息:")
    print(f"- 业务域: {BusinessDomain.objects.count()} 个")
    print(f"- 应用系统: {Application.objects.count()} 个")
    print(f"- 数据库分配: {Database.objects.filter(app_id__isnull=False).count()} 个")
    print(f"- RDS实例: {RDSInstance.objects.count()} 个")
    print(f"- 数据库: {Database.objects.count()} 个")

if __name__ == '__main__':
    create_test_data()
