# Generated by Django 4.2 on 2025-08-01 18:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rds_manager', '0008_add_app_id_to_database'),
    ]

    operations = [
        migrations.CreateModel(
            name='BizDomain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('external_id', models.BigIntegerField(blank=True, null=True, unique=True, verbose_name='外部系统ID')),
                ('name', models.CharField(max_length=100, verbose_name='业务域名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('owner', models.CharField(blank=True, max_length=50, null=True, verbose_name='负责人')),
                ('owner_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='负责人ID')),
                ('tech_owner', models.CharField(blank=True, max_length=50, null=True, verbose_name='技术负责人')),
                ('tech_owner_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='技术负责人ID')),
                ('pbu_owner', models.CharField(blank=True, max_length=50, null=True, verbose_name='PBU负责人')),
                ('pbu_owner_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='PBU负责人ID')),
                ('dept_id', models.BigIntegerField(blank=True, null=True, verbose_name='所属中心ID')),
                ('domain_type', models.IntegerField(default=0, verbose_name='业务域类型')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('listing_status', models.BooleanField(default=True, verbose_name='上架状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '业务域',
                'verbose_name_plural': '业务域',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('external_id', models.BigIntegerField(blank=True, null=True, unique=True, verbose_name='外部系统ID')),
                ('biz_domain_id', models.BigIntegerField(verbose_name='所属业务域ID')),
                ('name', models.CharField(max_length=100, verbose_name='产品线名称')),
                ('owner', models.CharField(blank=True, max_length=50, null=True, verbose_name='负责人')),
                ('owner_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='负责人ID')),
                ('bug_online_owner', models.CharField(blank=True, max_length=50, null=True, verbose_name='线上bug负责人')),
                ('bug_online_owner_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='线上bug负责人ID')),
                ('sr_expert', models.CharField(blank=True, max_length=50, null=True, verbose_name='SR专家')),
                ('sr_expert_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='SR专家ID')),
                ('product_line_level', models.IntegerField(blank=True, null=True, verbose_name='产品线等级')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('listing_status', models.BooleanField(default=True, verbose_name='上架状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '产品线',
                'verbose_name_plural': '产品线',
                'ordering': ['biz_domain_id', 'name'],
            },
        ),
        migrations.AddIndex(
            model_name='productline',
            index=models.Index(fields=['external_id'], name='product_line_external_id_idx'),
        ),
        migrations.AddIndex(
            model_name='productline',
            index=models.Index(fields=['biz_domain_id'], name='product_line_biz_domain_id_idx'),
        ),
        migrations.AddIndex(
            model_name='bizdomain',
            index=models.Index(fields=['external_id'], name='biz_domain_external_id_idx'),
        ),
        migrations.AddIndex(
            model_name='bizdomain',
            index=models.Index(fields=['dept_id'], name='biz_domain_dept_id_idx'),
        ),
    ]
