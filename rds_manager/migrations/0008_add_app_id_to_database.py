# Generated by Django 4.2 on 2025-08-01 17:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rds_manager', '0007_add_application_models'),
    ]

    operations = [
        migrations.AddField(
            model_name='database',
            name='app_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='关联应用ID'),
        ),
        migrations.AddField(
            model_name='database',
            name='assigned_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='分配时间'),
        ),
        migrations.AddField(
            model_name='database',
            name='assigned_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='分配人ID'),
        ),
        migrations.AddIndex(
            model_name='database',
            index=models.Index(fields=['app_id'], name='db_app_id_idx'),
        ),
        migrations.AddIndex(
            model_name='database',
            index=models.Index(fields=['instance_id'], name='db_instance_id_idx'),
        ),
    ]
