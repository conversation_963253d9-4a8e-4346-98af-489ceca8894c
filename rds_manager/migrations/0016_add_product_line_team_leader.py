# Generated by Django 4.2 on 2025-08-05 11:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rds_manager', '0015_alter_dashboarddailysummary_product_line_name'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='application',
            name='tech_owner_id',
        ),
        migrations.RemoveField(
            model_name='application',
            name='tech_owner_name',
        ),
        migrations.AddField(
            model_name='productline',
            name='team_leader_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='开发负责人ID'),
        ),
        migrations.AddField(
            model_name='productline',
            name='team_leader_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='开发负责人姓名'),
        ),
    ]
