# Generated by Django 4.2 on 2025-08-01 16:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rds_manager', '0006_slowqueryanalysis_analysis_sql_hash_inst_idx_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Application',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='应用名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='应用代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='应用描述')),
                ('business_domain_id', models.IntegerField(verbose_name='所属业务域ID')),
                ('app_type', models.CharField(choices=[('web', 'Web应用'), ('api', 'API服务'), ('batch', '批处理'), ('microservice', '微服务'), ('mobile', '移动应用'), ('desktop', '桌面应用'), ('other', '其他')], default='web', max_length=20, verbose_name='应用类型')),
                ('environment', models.CharField(choices=[('dev', '开发环境'), ('test', '测试环境'), ('staging', '预发布环境'), ('prod', '生产环境')], default='prod', max_length=20, verbose_name='环境')),
                ('version', models.CharField(blank=True, max_length=50, null=True, verbose_name='版本号')),
                ('tech_owner_id', models.IntegerField(blank=True, null=True, verbose_name='技术负责人ID')),
                ('business_owner_id', models.IntegerField(blank=True, null=True, verbose_name='业务负责人ID')),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='联系邮箱')),
                ('repository_url', models.URLField(blank=True, null=True, verbose_name='代码仓库地址')),
                ('documentation_url', models.URLField(blank=True, null=True, verbose_name='文档地址')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '应用系统',
                'verbose_name_plural': '应用系统',
                'ordering': ['business_domain_id', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ApplicationDatabase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('application_id', models.IntegerField(verbose_name='应用ID')),
                ('instance_id', models.CharField(max_length=100, verbose_name='RDS实例ID')),
                ('database_name', models.CharField(max_length=100, verbose_name='数据库名称')),
                ('relation_type', models.CharField(choices=[('primary', '主数据库'), ('secondary', '从数据库'), ('cache', '缓存数据库'), ('analytics', '分析数据库'), ('backup', '备份数据库'), ('temp', '临时数据库')], default='primary', max_length=20, verbose_name='关联类型')),
                ('access_type', models.CharField(choices=[('read_write', '读写'), ('read_only', '只读'), ('write_only', '只写')], default='read_write', max_length=20, verbose_name='访问类型')),
                ('connection_config', models.JSONField(blank=True, null=True, verbose_name='连接配置')),
                ('priority', models.IntegerField(default=1, verbose_name='优先级')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_by_id', models.IntegerField(blank=True, null=True, verbose_name='创建人ID')),
                ('approved_by_id', models.IntegerField(blank=True, null=True, verbose_name='审批人ID')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='审批时间')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '应用数据库关联',
                'verbose_name_plural': '应用数据库关联',
                'ordering': ['application_id', 'priority'],
            },
        ),
        migrations.CreateModel(
            name='BusinessDomain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='业务域名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='业务域代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='业务域描述')),
                ('team_leader_id', models.IntegerField(blank=True, null=True, verbose_name='团队负责人ID')),
                ('supervisor_id', models.IntegerField(blank=True, null=True, verbose_name='总监ID')),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='联系邮箱')),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '业务域',
                'verbose_name_plural': '业务域',
                'ordering': ['name'],
            },
        ),
        migrations.AddIndex(
            model_name='applicationdatabase',
            index=models.Index(fields=['application_id'], name='appdb_application_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationdatabase',
            index=models.Index(fields=['instance_id'], name='appdb_instance_id_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationdatabase',
            index=models.Index(fields=['database_name'], name='appdb_database_name_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationdatabase',
            index=models.Index(fields=['relation_type'], name='appdb_relation_type_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationdatabase',
            index=models.Index(fields=['is_active'], name='appdb_is_active_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='applicationdatabase',
            unique_together={('application_id', 'instance_id', 'database_name')},
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['business_domain_id'], name='app_business_domain_idx'),
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['code'], name='app_code_idx'),
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['environment'], name='app_environment_idx'),
        ),
        migrations.AddIndex(
            model_name='application',
            index=models.Index(fields=['is_active'], name='app_is_active_idx'),
        ),
    ]
