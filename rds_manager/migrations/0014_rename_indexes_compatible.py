# Generated by Django 4.2 on 2025-08-04 16:01
# Modified for Django 3.7 compatibility

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rds_manager', '0013_dashboarddailysummary_dashboardsnapshotlog_and_more'),
    ]

    operations = [
        # Remove old indexes
        migrations.RemoveIndex(
            model_name='dashboarddailysummary',
            name='dashboard_snapshot_date_idx',
        ),
        migrations.RemoveIndex(
            model_name='dashboarddailysummary',
            name='dashboard_domain_date_idx',
        ),
        migrations.RemoveIndex(
            model_name='dashboarddailysummary',
            name='dashboard_product_date_idx',
        ),
        migrations.RemoveIndex(
            model_name='dashboarddailysummary',
            name='dashboard_date_domain_idx',
        ),
        migrations.RemoveIndex(
            model_name='dashboardsnapshotlog',
            name='snapshot_log_date_idx',
        ),
        migrations.RemoveIndex(
            model_name='dashboardsnapshotlog',
            name='snapshot_log_status_idx',
        ),
        migrations.RemoveIndex(
            model_name='dashboardsnapshotlog',
            name='snapshot_log_created_idx',
        ),
        
        # Add new indexes with new names
        migrations.AddIndex(
            model_name='dashboarddailysummary',
            index=models.Index(fields=['snapshot_date'], name='dash_snap_date_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboarddailysummary',
            index=models.Index(fields=['biz_domain_id', 'snapshot_date'], name='dash_domain_date_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboarddailysummary',
            index=models.Index(fields=['product_line_id', 'snapshot_date'], name='dash_product_date_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboarddailysummary',
            index=models.Index(fields=['snapshot_date', 'biz_domain_id'], name='dash_date_domain_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboardsnapshotlog',
            index=models.Index(fields=['snapshot_date'], name='snap_log_date_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboardsnapshotlog',
            index=models.Index(fields=['status'], name='snap_log_status_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboardsnapshotlog',
            index=models.Index(fields=['created_at'], name='snap_log_created_idx'),
        ),
    ]
