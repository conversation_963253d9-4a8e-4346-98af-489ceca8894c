# Generated by Django 4.2 on 2025-08-04 15:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rds_manager', '0012_add_tech_owner_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardDailySummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('biz_domain_id', models.BigIntegerField(verbose_name='业务域ID')),
                ('biz_domain_name', models.CharField(max_length=100, verbose_name='业务域名称')),
                ('product_line_id', models.BigIntegerField(blank=True, null=True, verbose_name='产品线ID')),
                ('product_line_name', models.CharField(blank=True, max_length=100, verbose_name='产品线名称')),
                ('snapshot_date', models.DateField(verbose_name='快照日期')),
                ('sql_execution_count', models.BigIntegerField(default=0, verbose_name='SQL执行总数')),
                ('sql_template_count', models.IntegerField(default=0, verbose_name='SQL模板数量')),
                ('avg_query_time', models.FloatField(default=0, verbose_name='平均查询时间(秒)')),
                ('total_query_time', models.FloatField(default=0, verbose_name='总查询时间(秒)')),
                ('slow_query_count', models.BigIntegerField(default=0, verbose_name='慢查询总数')),
                ('slow_query_template_count', models.IntegerField(default=0, verbose_name='慢查询模板数量')),
                ('avg_slow_query_time', models.FloatField(default=0, verbose_name='慢查询平均时间(秒)')),
                ('max_slow_query_time', models.FloatField(default=0, verbose_name='慢查询最大时间(秒)')),
                ('high_risk_sql_count', models.IntegerField(default=0, verbose_name='高风险SQL数量')),
                ('medium_risk_sql_count', models.IntegerField(default=0, verbose_name='中风险SQL数量')),
                ('low_risk_sql_count', models.IntegerField(default=0, verbose_name='低风险SQL数量')),
                ('avg_risk_score', models.FloatField(default=0, verbose_name='平均风险评分')),
                ('database_count', models.IntegerField(default=0, verbose_name='数据库数量')),
                ('table_count', models.IntegerField(default=0, verbose_name='表数量')),
                ('total_table_rows', models.BigIntegerField(default=0, verbose_name='总表行数')),
                ('avg_table_rows', models.BigIntegerField(default=0, verbose_name='平均表行数')),
                ('large_table_count', models.IntegerField(default=0, verbose_name='大表数量(>200万行)')),
                ('total_index_count', models.IntegerField(default=0, verbose_name='总索引数量')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '数据库运行情况日统计',
                'verbose_name_plural': '数据库运行情况日统计',
                'ordering': ['-snapshot_date', 'biz_domain_name'],
            },
        ),
        migrations.CreateModel(
            name='DashboardSnapshotLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('snapshot_date', models.DateField(verbose_name='快照日期')),
                ('start_time', models.DateTimeField(verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('status', models.CharField(choices=[('running', '执行中'), ('success', '成功'), ('failed', '失败'), ('partial', '部分成功')], default='running', max_length=20, verbose_name='执行状态')),
                ('processed_domains', models.IntegerField(default=0, verbose_name='处理的业务域数量')),
                ('processed_records', models.IntegerField(default=0, verbose_name='生成的记录数量')),
                ('error_count', models.IntegerField(default=0, verbose_name='错误数量')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('execution_details', models.JSONField(default=dict, verbose_name='执行详情')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '快照生成日志',
                'verbose_name_plural': '快照生成日志',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='dashboardsnapshotlog',
            index=models.Index(fields=['snapshot_date'], name='snapshot_log_date_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboardsnapshotlog',
            index=models.Index(fields=['status'], name='snapshot_log_status_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboardsnapshotlog',
            index=models.Index(fields=['created_at'], name='snapshot_log_created_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboarddailysummary',
            index=models.Index(fields=['snapshot_date'], name='dashboard_snapshot_date_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboarddailysummary',
            index=models.Index(fields=['biz_domain_id', 'snapshot_date'], name='dashboard_domain_date_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboarddailysummary',
            index=models.Index(fields=['product_line_id', 'snapshot_date'], name='dashboard_product_date_idx'),
        ),
        migrations.AddIndex(
            model_name='dashboarddailysummary',
            index=models.Index(fields=['snapshot_date', 'biz_domain_id'], name='dashboard_date_domain_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='dashboarddailysummary',
            unique_together={('biz_domain_id', 'product_line_id', 'snapshot_date')},
        ),
    ]
