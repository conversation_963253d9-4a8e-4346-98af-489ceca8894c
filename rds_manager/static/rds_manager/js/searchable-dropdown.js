/**
 * 通用可搜索下拉框组件
 * 基于高风险SQL页面的搜索框实现
 * 
 * 使用方法：
 * 1. 在HTML中创建下拉框结构
 * 2. 调用 initializeSearchableDropdown(config) 初始化
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

class SearchableDropdown {
    constructor(config) {
        this.config = {
            dropdownId: '',
            searchInputId: '',
            hiddenInputId: '',
            filterInputId: '',
            optionsContainerId: '',
            placeholder: '请选择...',
            searchPlaceholder: '搜索...',
            allowClear: true,
            onChange: null,
            ...config
        };
        
        this.dropdown = null;
        this.searchInput = null;
        this.hiddenInput = null;
        this.filterInput = null;
        this.optionsContainer = null;
        
        this.init();
    }
    
    init() {
        // 获取DOM元素
        this.dropdown = document.getElementById(this.config.dropdownId);
        this.searchInput = document.getElementById(this.config.searchInputId);
        this.hiddenInput = document.getElementById(this.config.hiddenInputId);
        this.filterInput = document.getElementById(this.config.filterInputId);
        this.optionsContainer = document.getElementById(this.config.optionsContainerId);
        
        if (!this.validateElements()) {
            console.error('SearchableDropdown: 必要的DOM元素未找到', this.config);
            return false;
        }
        
        this.bindEvents();
        this.setInitialValue();
        
        return true;
    }
    
    validateElements() {
        return this.dropdown && this.searchInput && this.hiddenInput && 
               this.filterInput && this.optionsContainer;
    }
    
    bindEvents() {
        // 点击输入框显示/隐藏下拉菜单
        this.searchInput.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggle();
        });
        
        // 搜索过滤
        this.filterInput.addEventListener('input', (e) => {
            this.filterOptions(e.target.value);
        });
        
        // 选项点击事件
        this.optionsContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('dropdown-option')) {
                this.selectOption(e.target);
            }
        });
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!this.dropdown.contains(e.target)) {
                this.close();
            }
        });
        
        // 键盘事件
        this.filterInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
    }
    
    toggle() {
        if (this.dropdown.classList.contains('open')) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        this.dropdown.classList.add('open');
        this.filterInput.focus();
        this.filterInput.value = '';
        this.showAllOptions();
    }
    
    close() {
        this.dropdown.classList.remove('open');
    }
    
    filterOptions(searchTerm) {
        const term = searchTerm.toLowerCase();
        const options = this.optionsContainer.querySelectorAll('.dropdown-option');
        
        options.forEach(option => {
            const text = option.textContent.toLowerCase();
            const dataText = option.getAttribute('data-text');
            const searchText = dataText ? dataText.toLowerCase() : text;
            
            if (searchText.includes(term)) {
                option.classList.remove('hidden');
            } else {
                option.classList.add('hidden');
            }
        });
    }
    
    showAllOptions() {
        const options = this.optionsContainer.querySelectorAll('.dropdown-option');
        options.forEach(option => {
            option.classList.remove('hidden');
        });
    }
    
    selectOption(optionElement) {
        const value = optionElement.getAttribute('data-value');
        const text = optionElement.getAttribute('data-text') || optionElement.textContent;
        
        // 更新选中状态
        this.optionsContainer.querySelectorAll('.dropdown-option').forEach(opt => {
            opt.classList.remove('selected');
        });
        optionElement.classList.add('selected');
        
        // 更新输入框和隐藏字段
        this.searchInput.value = text;
        this.hiddenInput.value = value;
        
        // 关闭下拉菜单
        this.close();
        
        // 触发change事件
        if (this.hiddenInput.dispatchEvent) {
            this.hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
        
        // 调用回调函数
        if (typeof this.config.onChange === 'function') {
            this.config.onChange(value, text, optionElement);
        }
    }
    
    setValue(value) {
        const option = this.optionsContainer.querySelector(`[data-value="${value}"]`);
        if (option) {
            this.selectOption(option);
        } else {
            // 如果没找到对应选项，设置为空
            this.clear();
        }
    }
    
    getValue() {
        return this.hiddenInput.value;
    }
    
    getText() {
        return this.searchInput.value;
    }
    
    clear() {
        this.searchInput.value = this.config.placeholder;
        this.hiddenInput.value = '';
        this.optionsContainer.querySelectorAll('.dropdown-option').forEach(opt => {
            opt.classList.remove('selected');
        });
    }
    
    setInitialValue() {
        const initialValue = this.hiddenInput.value;
        if (initialValue) {
            this.setValue(initialValue);
        } else {
            this.searchInput.value = this.config.placeholder;
        }
    }
    
    handleKeydown(e) {
        const visibleOptions = Array.from(this.optionsContainer.querySelectorAll('.dropdown-option:not(.hidden)'));
        const currentSelected = this.optionsContainer.querySelector('.dropdown-option.highlighted');
        let currentIndex = currentSelected ? visibleOptions.indexOf(currentSelected) : -1;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentIndex = Math.min(currentIndex + 1, visibleOptions.length - 1);
                this.highlightOption(visibleOptions[currentIndex]);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                currentIndex = Math.max(currentIndex - 1, 0);
                this.highlightOption(visibleOptions[currentIndex]);
                break;
                
            case 'Enter':
                e.preventDefault();
                if (currentSelected) {
                    this.selectOption(currentSelected);
                }
                break;
                
            case 'Escape':
                e.preventDefault();
                this.close();
                break;
        }
    }
    
    highlightOption(option) {
        // 移除之前的高亮
        this.optionsContainer.querySelectorAll('.dropdown-option').forEach(opt => {
            opt.classList.remove('highlighted');
        });
        
        // 添加新的高亮
        if (option) {
            option.classList.add('highlighted');
            option.scrollIntoView({ block: 'nearest' });
        }
    }
    
    addOption(value, text, insertAtBeginning = false) {
        const option = document.createElement('div');
        option.className = 'dropdown-option';
        option.setAttribute('data-value', value);
        option.setAttribute('data-text', text);
        option.textContent = text;
        
        if (insertAtBeginning) {
            this.optionsContainer.insertBefore(option, this.optionsContainer.firstChild);
        } else {
            this.optionsContainer.appendChild(option);
        }
    }
    
    removeOption(value) {
        const option = this.optionsContainer.querySelector(`[data-value="${value}"]`);
        if (option) {
            option.remove();
        }
    }
    
    updateOptions(options) {
        // 清空现有选项
        this.optionsContainer.innerHTML = '';
        
        // 添加新选项
        options.forEach(option => {
            this.addOption(option.value, option.text);
        });
    }
    
    disable() {
        this.dropdown.style.opacity = '0.6';
        this.dropdown.style.pointerEvents = 'none';
        this.searchInput.disabled = true;
    }
    
    enable() {
        this.dropdown.style.opacity = '1';
        this.dropdown.style.pointerEvents = 'auto';
        this.searchInput.disabled = false;
    }
}

// 全局初始化函数，保持向后兼容
function initializeSearchableDropdown(config) {
    return new SearchableDropdown(config);
}

// 批量初始化函数
function initializeSearchableDropdowns(configs) {
    return configs.map(config => new SearchableDropdown(config));
}

// 导出到全局
window.SearchableDropdown = SearchableDropdown;
window.initializeSearchableDropdown = initializeSearchableDropdown;
window.initializeSearchableDropdowns = initializeSearchableDropdowns;
