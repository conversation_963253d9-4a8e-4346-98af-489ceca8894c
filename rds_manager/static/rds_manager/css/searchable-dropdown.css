/**
 * 通用可搜索下拉框样式
 * 基于高风险SQL页面的搜索框样式
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

/* 可搜索下拉框样式 */
.searchable-dropdown {
    position: relative;
    width: 100%;
}

.dropdown-input-container {
    position: relative;
    cursor: pointer;
}

.dropdown-search {
    cursor: pointer;
    padding-right: 30px;
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.dropdown-search:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.dropdown-search:disabled {
    background-color: #e9ecef;
    opacity: 1;
    cursor: not-allowed;
}

.dropdown-arrow {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #6c757d;
    font-size: 12px;
    transition: transform 0.2s ease;
}

.searchable-dropdown.open .dropdown-arrow {
    transform: translateY(-50%) rotate(180deg);
}

.dropdown-menu-custom {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 100%;
    width: max-content;
    max-width: 400px;
    background: white;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    z-index: 1000;
    max-height: 240px;
    overflow: hidden;
    display: none;
    margin-top: 2px;
}

.searchable-dropdown.open .dropdown-menu-custom {
    display: block;
}

.dropdown-search-container {
    padding: 6px 10px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    min-width: 250px;
}

.dropdown-search-container input {
    width: 100%;
    min-width: 200px;
    padding: 4px 8px;
    font-size: 13px;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    outline: none;
}

.dropdown-search-container input:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.125rem rgba(13, 110, 253, 0.25);
}

.dropdown-options {
    max-height: 200px;
    overflow-y: auto;
    padding: 2px 0;
}

.dropdown-option {
    padding: 6px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.15s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 200px;
    font-size: 14px;
    line-height: 1.3;
    color: #495057;
}

.dropdown-option:hover {
    background-color: #e9ecef;
}

.dropdown-option.selected {
    background-color: #0d6efd;
    color: white;
}

.dropdown-option.highlighted {
    background-color: #f8f9fa;
    border-left: 3px solid #0d6efd;
}

.dropdown-option:last-child {
    border-bottom: none;
}

.dropdown-option.hidden {
    display: none;
}

/* 滚动条样式 */
.dropdown-options::-webkit-scrollbar {
    width: 6px;
}

.dropdown-options::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dropdown-menu-custom {
        max-width: 90vw;
        left: -10px;
    }

    .dropdown-search-container {
        min-width: auto;
        padding: 5px 8px;
    }

    .dropdown-option {
        min-width: auto;
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* 确保下拉框在小屏幕上不会超出视口 */
@media (max-width: 576px) {
    .dropdown-menu-custom {
        left: -20px;
        right: -20px;
        max-width: none;
        width: auto;
    }
}

/* 禁用状态样式 */
.searchable-dropdown.disabled {
    opacity: 0.6;
    pointer-events: none;
}

.searchable-dropdown.disabled .dropdown-search {
    background-color: #e9ecef;
    cursor: not-allowed;
}

/* 加载状态样式 */
.dropdown-loading {
    padding: 12px;
    text-align: center;
    color: #6c757d;
    font-size: 13px;
}

.dropdown-loading::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.dropdown-empty {
    padding: 12px;
    text-align: center;
    color: #6c757d;
    font-size: 13px;
    font-style: italic;
}

/* 分组样式 */
.dropdown-group-header {
    padding: 6px 12px;
    background-color: #f8f9fa;
    color: #6c757d;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #e9ecef;
    cursor: default;
}

.dropdown-group-option {
    padding-left: 24px;
}

/* 多选模式样式 */
.dropdown-option.multi-selected {
    background-color: #e7f3ff;
    color: #0056b3;
}

.dropdown-option.multi-selected::after {
    content: '✓';
    float: right;
    color: #0056b3;
    font-weight: bold;
}

/* 清除按钮样式 */
.dropdown-clear {
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    width: 16px;
    height: 16px;
    display: none;
}

.searchable-dropdown.has-value .dropdown-clear {
    display: block;
}

.dropdown-clear:hover {
    color: #495057;
}

/* 错误状态样式 */
.searchable-dropdown.error .dropdown-search {
    border-color: #dc3545;
}

.searchable-dropdown.error .dropdown-search:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

/* 成功状态样式 */
.searchable-dropdown.success .dropdown-search {
    border-color: #28a745;
}

.searchable-dropdown.success .dropdown-search:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}
