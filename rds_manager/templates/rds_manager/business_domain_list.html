{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}业务域管理{% endblock %}

{% block extra_css %}
<style>
    .domain-card {
        border: 1px solid #e3e6f0;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s, box-shadow 0.2s;
    }
    .domain-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    .domain-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .domain-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
    .domain-code {
        background: #007bff;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    .domain-info {
        margin-bottom: 15px;
    }
    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }
    .info-item i {
        width: 20px;
        margin-right: 8px;
        color: #6c757d;
    }
    .domain-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }
    .stat-item {
        text-align: center;
    }
    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #007bff;
    }
    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
    }
    .no-data {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-building"></i> 业务域管理
                </h1>
                <div>
                    <a href="{% url 'rds_manager:application_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line"></i> 返回仪表盘
                    </a>
                    <a href="/admin/rds_manager/businessdomain/add/" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 新增业务域
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if domains %}
        <div class="row">
            {% for domain in domains %}
            <div class="col-md-6 col-lg-4">
                <div class="domain-card">
                    <div class="domain-header">
                        <h5 class="domain-title">{{ domain.name }}</h5>
                        <span class="domain-code">{{ domain.code }}</span>
                    </div>
                    
                    <div class="domain-info">
                        {% if domain.description %}
                        <div class="info-item">
                            <i class="fas fa-info-circle"></i>
                            <span>{{ domain.description|truncatechars:50 }}</span>
                        </div>
                        {% endif %}
                        
                        {% if domain.team_leader %}
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <span>负责人: {{ domain.team_leader.username }}</span>
                        </div>
                        {% endif %}
                        
                        {% if domain.supervisor %}
                        <div class="info-item">
                            <i class="fas fa-crown"></i>
                            <span>总监: {{ domain.supervisor.username }}</span>
                        </div>
                        {% endif %}
                        
                        {% if domain.contact_email %}
                        <div class="info-item">
                            <i class="fas fa-envelope"></i>
                            <span>{{ domain.contact_email }}</span>
                        </div>
                        {% endif %}
                        
                        {% if domain.contact_phone %}
                        <div class="info-item">
                            <i class="fas fa-phone"></i>
                            <span>{{ domain.contact_phone }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="domain-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ domain.app_count }}</div>
                            <div class="stat-label">应用数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ domain.database_count }}</div>
                            <div class="stat-label">数据库数量</div>
                        </div>
                        <div class="stat-item">
                            <small class="text-muted">
                                创建于<br>{{ domain.created_at|date:"Y-m-d" }}
                            </small>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="{% url 'rds_manager:application_list' %}?domain={{ domain.id }}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cube"></i> 查看应用
                        </a>
                        <a href="/admin/rds_manager/businessdomain/{{ domain.id }}/change/" 
                           class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if page_obj.has_other_pages %}
        <div class="row">
            <div class="col-12">
                <nav aria-label="业务域分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}

    {% else %}
        <div class="row">
            <div class="col-12">
                <div class="no-data">
                    <i class="fas fa-building fa-3x mb-3"></i>
                    <h4>暂无业务域</h4>
                    <p>还没有创建任何业务域，点击上方按钮开始创建。</p>
                    <a href="/admin/rds_manager/businessdomain/add/" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 创建第一个业务域
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
