{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}数据库分配管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'rds_manager/css/searchable-dropdown.css' %}">
<style>
    .search-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .database-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .app-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
        background: #e7f3ff;
        color: #0056b3;
    }
    .unassigned-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
        background: #f8f9fa;
        color: #6c757d;
    }
    .edit-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        cursor: pointer;
    }
    .edit-btn:hover {
        background: #218838;
    }

    /* 模态框内的搜索框特殊样式 */
    .modal .searchable-dropdown .dropdown-menu-custom {
        z-index: 1060; /* 确保在模态框之上 */
    }

    /* 强制显示打开状态的下拉菜单 */
    .modal .searchable-dropdown.open .dropdown-menu-custom {
        display: block !important;
        z-index: 1060;
    }

    /* 下拉箭头旋转动画 */
    .searchable-dropdown .dropdown-arrow {
        transition: transform 0.2s ease;
    }

    .searchable-dropdown.open .dropdown-arrow {
        transform: rotate(180deg);
    }
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }

    /* 数据库名称列样式 */
    .database-name-cell {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .database-name-cell strong {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-database"></i> 数据库分配管理
                </h1>
                <div>
                    <a href="{% url 'rds_manager:application_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line"></i> 返回仪表盘
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="row">
        <div class="col-12">
            <div class="search-card">
                <form method="get" class="row g-3">
                    {% csrf_token %}
                    <div class="col-md-3">
                        <label for="search" class="form-label">搜索数据库名或实例名</label>
                        <div class="input-group">
                            <input type="text" name="search" id="search" class="form-control"
                                   value="{{ search_query }}" placeholder="输入数据库名或实例名">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="instance" class="form-label">RDS实例</label>
                        <div class="searchable-dropdown" id="instanceDropdown">
                            <div class="dropdown-input-container">
                                <input type="text" id="instanceSearch" class="form-control dropdown-search" placeholder="请选择实例" readonly>
                                <input type="hidden" id="instance" name="instance" value="{{ current_instance }}">
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            <div class="dropdown-menu-custom" id="instanceDropdownMenu">
                                <div class="dropdown-search-container">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索实例..." id="instanceSearchInput">
                                </div>
                                <div class="dropdown-options" id="instanceOptions">
                                    <div class="dropdown-option" data-value="" data-text="全部实例">全部实例</div>
                                    {% for instance in instances %}
                                    <div class="dropdown-option" data-value="{{ instance.instance_id }}" data-text="{{ instance.instance_name }} ({{ instance.instance_id }})">
                                        {{ instance.instance_name }} ({{ instance.instance_id }})
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="bizDomain" class="form-label">业务域</label>
                        <div class="searchable-dropdown" id="bizDomainDropdown">
                            <div class="dropdown-input-container">
                                <input type="text" id="bizDomainSearch" class="form-control dropdown-search" placeholder="全部业务域" readonly>
                                <input type="hidden" id="bizDomain" name="biz_domain" value="{{ current_biz_domain }}">
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            <div class="dropdown-menu-custom" id="bizDomainDropdownMenu">
                                <div class="dropdown-search-container">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索业务域..." id="bizDomainSearchInput">
                                </div>
                                <div class="dropdown-options" id="bizDomainOptions">
                                    <div class="dropdown-option" data-value="" data-text="全部业务域">全部业务域</div>
                                    {% for domain in business_domains %}
                                    <div class="dropdown-option" data-value="{{ domain.external_id }}" data-text="{{ domain.name }}">
                                        {{ domain.name }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="productLine" class="form-label">产品线</label>
                        <div class="searchable-dropdown" id="productLineDropdown">
                            <div class="dropdown-input-container">
                                <input type="text" id="productLineSearch" class="form-control dropdown-search" placeholder="全部产品线" readonly>
                                <input type="hidden" id="productLine" name="product_line" value="{{ current_product_line }}">
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            <div class="dropdown-menu-custom" id="productLineDropdownMenu">
                                <div class="dropdown-search-container">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索产品线..." id="productLineSearchInput">
                                </div>
                                <div class="dropdown-options" id="productLineOptions">
                                    <div class="dropdown-option" data-value="" data-text="全部产品线">全部产品线</div>
                                    {% for line in product_lines %}
                                    <div class="dropdown-option" data-value="{{ line.id }}" data-text="{{ line.name }}" data-domain="{{ line.biz_domain_id }}">
                                        {{ line.name }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="application" class="form-label">应用</label>
                        <div class="searchable-dropdown" id="applicationDropdown">
                            <div class="dropdown-input-container">
                                <input type="text" id="applicationSearch" class="form-control dropdown-search" placeholder="全部应用" readonly>
                                <input type="hidden" id="application" name="application" value="{{ current_application }}">
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            <div class="dropdown-menu-custom" id="applicationDropdownMenu">
                                <div class="dropdown-search-container">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索应用..." id="applicationSearchInput">
                                </div>
                                <div class="dropdown-options" id="applicationOptions">
                                    <div class="dropdown-option" data-value="" data-text="全部应用">全部应用</div>
                                    <div class="dropdown-option" data-value="unassigned" data-text="未分配">未分配</div>
                                    {% for app in applications %}
                                    <div class="dropdown-option" data-value="{{ app.id }}" data-text="{{ app.name }}" data-product-line="{{ app.product_line_id }}">
                                        {{ app.name }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>


                </form>

                <!-- 管理按钮 -->

            </div>
        </div>
    </div>

    <!-- 应用管理栏 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="app-management-bar">
                <div class="d-flex justify-content-between align-items-center p-3" style="background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;">
                    <div>
                        <h6 class="mb-0 text-muted">
                            <i class="fas fa-cogs"></i> 应用管理
                        </h6>
                        <small class="text-muted">全局绑定管理功能</small>
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary" onclick="openAppToProductLineModal()">
                            <i class="fas fa-link"></i> 应用绑定产品线
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="openDatabaseToAppModal()">
                            <i class="fas fa-database"></i> 数据库绑定应用
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据库列表 -->
    {% if databases %}
    <div class="row">
        <div class="col-12">
            <div class="database-table">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>数据库名</th>
                                <th>RDS实例ID</th>
                                <th>RDS实例名称</th>
                                <th>所属应用</th>
                                <th>业务线</th>
                                <th>负责人</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for db in databases %}
                            <tr id="db-row-{{ db.id }}">
                                <td>{{ db.id }}</td>
                                <td class="database-name-cell"><strong title="{{ db.name }}">{{ db.name }}</strong></td>
                                <td><code>{{ db.instance_id }}</code></td>
                                <td>
                                    {% if db.instance %}
                                        {{ db.instance.instance_name }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td id="app-cell-{{ db.id }}">
                                    {% if db.application %}
                                        <span class="app-badge">{{ db.application.name }}</span>
                                    {% else %}
                                        <span class="unassigned-badge">未分配</span>
                                    {% endif %}
                                </td>
                                <td id="domain-cell-{{ db.id }}">
                                    {% if db.application and db.application.product_line %}
                                        <span class="text-success">{{ db.application.product_line.name }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td id="owner-cell-{{ db.id }}">
                                    {% if db.application and db.application.product_line and db.application.product_line.team_leader_display %}
                                        <span class="text-primary">{{ db.application.product_line.team_leader_display }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="edit-btn" onclick="openEditModal({{ db.id }}, '{{ db.name|escapejs }}', '{{ db.instance_id|escapejs }}', '{% if db.instance %}{{ db.instance.instance_name|escapejs }}{% else %}{{ db.instance_id|escapejs }}{% endif %}', {{ db.app_id|default:'null' }}, {% if db.application and db.application.product_line_id %}{{ db.application.product_line_id }}{% else %}null{% endif %}, '{% if db.application and db.application.product_line and db.application.product_line.team_leader_display %}{{ db.application.product_line.team_leader_display|escapejs }}{% else %}{% endif %}')">
                                        编辑
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="数据库分页">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_instance %}&instance={{ current_instance }}{% endif %}{% if current_app %}&app={{ current_app }}{% endif %}">首页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_instance %}&instance={{ current_instance }}{% endif %}{% if current_app %}&app={{ current_app }}{% endif %}">上一页</a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_instance %}&instance={{ current_instance }}{% endif %}{% if current_app %}&app={{ current_app }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_instance %}&instance={{ current_instance }}{% endif %}{% if current_app %}&app={{ current_app }}{% endif %}">下一页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_instance %}&instance={{ current_instance }}{% endif %}{% if current_app %}&app={{ current_app }}{% endif %}">末页</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-database fa-3x text-muted mb-3"></i>
                <h4>暂无数据库</h4>
                <p class="text-muted">没有找到匹配的数据库，请调整搜索条件。</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 编辑模态框 -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">数据库信息编辑</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="databaseId" name="database_id">
                    
                    <div class="mb-3">
                        <label for="databaseName" class="form-label">数据库名称</label>
                        <input type="text" class="form-control" id="databaseName" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="instanceId" class="form-label">RDS实例ID</label>
                        <input type="text" class="form-control" id="instanceId" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="instanceName" class="form-label">RDS实例名称</label>
                        <input type="text" class="form-control" id="instanceName" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="appSelect" class="form-label">所属应用</label>
                        <div class="searchable-dropdown" id="appDropdown">
                            <div class="dropdown-input-container">
                                <input type="text" id="appSearch" class="form-control dropdown-search" placeholder="请选择应用" readonly>
                                <input type="hidden" id="appSelect" name="app_id" value="">
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            <div class="dropdown-menu-custom" id="appDropdownMenu">
                                <div class="dropdown-search-container">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索应用..." id="appSearchInput">
                                </div>
                                <div class="dropdown-options" id="appOptions">
                                    <div class="dropdown-option" data-value="" data-text="未分配">未分配</div>
                                    {% for app in applications %}
                                    <div class="dropdown-option" data-value="{{ app.id }}" data-text="{{ app.name }}">
                                        {{ app.name }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="businessDomain" class="form-label">所属业务线</label>
                        <div class="searchable-dropdown" id="businessDomainDropdown">
                            <div class="dropdown-input-container">
                                <input type="text" id="businessDomainSearch" class="form-control dropdown-search" placeholder="请选择业务线" readonly>
                                <input type="hidden" id="businessDomain" name="product_line_id" value="">
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            <div class="dropdown-menu-custom" id="businessDomainDropdownMenu">
                                <div class="dropdown-search-container">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索业务线..." id="businessDomainSearchInput">
                                </div>
                                <div class="dropdown-options" id="businessDomainOptions">
                                    <div class="dropdown-option" data-value="" data-text="未分配">未分配</div>
                                    {% for product_line in product_lines %}
                                    <div class="dropdown-option" data-value="{{ product_line.id }}" data-text="{{ product_line.name }}">
                                        {{ product_line.name }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="techOwner" class="form-label">负责人</label>
                        <input type="text" class="form-control" id="techOwner" name="tech_owner">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveAssignment()">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- 应用绑定到产品线模态框 -->
<div class="modal fade" id="appToProductLineModal" tabindex="-1" aria-labelledby="appToProductLineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="appToProductLineModalLabel">应用绑定到产品线</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="appToProductLineForm">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appToProductLineAppSelect" class="form-label">选择应用</label>
                                <div class="searchable-dropdown" id="appToProductLineAppDropdown">
                                    <div class="dropdown-input-container">
                                        <input type="text" id="appToProductLineAppSearch" class="form-control dropdown-search" placeholder="请选择应用" readonly>
                                        <input type="hidden" id="appToProductLineAppSelect" name="app_id" required>
                                        <span class="dropdown-arrow">▼</span>
                                    </div>
                                    <div class="dropdown-menu-custom">
                                        <div class="dropdown-search-container">
                                            <input type="text" class="form-control form-control-sm" id="appToProductLineAppSearchInput" placeholder="搜索应用...">
                                        </div>
                                        <div class="dropdown-options" id="appToProductLineAppOptions">
                                            <div class="dropdown-option" data-value="" data-text="请选择应用">请选择应用</div>
                                            {% for app in applications %}
                                            <div class="dropdown-option" data-value="{{ app.id }}" data-current-product-line="{{ app.product_line_id|default:'' }}" data-text="{{ app.name }}{% if app.product_line %} (当前: {{ app.product_line.name }}){% else %} (未绑定){% endif %}">
                                                {{ app.name }}
                                                {% if app.product_line %}
                                                    (当前: {{ app.product_line.name }})
                                                {% else %}
                                                    (未绑定)
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appToProductLineProductLineSelect" class="form-label">选择产品线</label>
                                <div class="searchable-dropdown" id="appToProductLineProductLineDropdown">
                                    <div class="dropdown-input-container">
                                        <input type="text" id="appToProductLineProductLineSearch" class="form-control dropdown-search" placeholder="请选择产品线" readonly>
                                        <input type="hidden" id="appToProductLineProductLineSelect" name="product_line_id" required>
                                        <span class="dropdown-arrow">▼</span>
                                    </div>
                                    <div class="dropdown-menu-custom">
                                        <div class="dropdown-search-container">
                                            <input type="text" class="form-control form-control-sm" id="appToProductLineProductLineSearchInput" placeholder="搜索产品线...">
                                        </div>
                                        <div class="dropdown-options" id="appToProductLineProductLineOptions">
                                            <div class="dropdown-option" data-value="" data-text="请选择产品线">请选择产品线</div>
                                            {% for line in product_lines %}
                                            <div class="dropdown-option" data-value="{{ line.id }}" data-text="{{ line.name }}">{{ line.name }}</div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>说明：</strong>选择应用和产品线进行绑定。如果应用已绑定其他产品线，将会更新绑定关系。
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveAppToProductLineBinding()">确定绑定</button>
            </div>
        </div>
    </div>
</div>

<!-- 数据库绑定到应用模态框 -->
<div class="modal fade" id="databaseToAppModal" tabindex="-1" aria-labelledby="databaseToAppModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="databaseToAppModalLabel">数据库绑定到应用</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="databaseToAppForm">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="databaseToAppDatabaseSelect" class="form-label">选择数据库</label>
                                <div class="searchable-dropdown" id="databaseToAppDatabaseDropdown">
                                    <div class="dropdown-input-container">
                                        <input type="text" id="databaseToAppDatabaseSearch" class="form-control dropdown-search" placeholder="请选择数据库" readonly>
                                        <input type="hidden" id="databaseToAppDatabaseSelect" name="database_id" required>
                                        <span class="dropdown-arrow">▼</span>
                                    </div>
                                    <div class="dropdown-menu-custom">
                                        <div class="dropdown-search-container">
                                            <input type="text" class="form-control form-control-sm" id="databaseToAppDatabaseSearchInput" placeholder="搜索数据库...">
                                        </div>
                                        <div class="dropdown-options" id="databaseToAppDatabaseOptions">
                                            <div class="dropdown-option" data-value="" data-text="请选择数据库">请选择数据库</div>
                                            {% for db in all_databases %}
                                            <div class="dropdown-option" data-value="{{ db.id }}" data-current-app="{{ db.app_id|default:'' }}" data-text="{{ db.name }} ({{ db.instance_id }}){% if db.application %} - 当前: {{ db.application.name }}{% else %} - 未分配{% endif %}">
                                                {{ db.name }} ({{ db.instance_id }})
                                                {% if db.application %}
                                                    - 当前: {{ db.application.name }}
                                                {% else %}
                                                    - 未分配
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="databaseToAppAppSelect" class="form-label">选择应用</label>
                                <div class="searchable-dropdown" id="databaseToAppAppDropdown">
                                    <div class="dropdown-input-container">
                                        <input type="text" id="databaseToAppAppSearch" class="form-control dropdown-search" placeholder="请选择应用" readonly>
                                        <input type="hidden" id="databaseToAppAppSelect" name="app_id" required>
                                        <span class="dropdown-arrow">▼</span>
                                    </div>
                                    <div class="dropdown-menu-custom">
                                        <div class="dropdown-search-container">
                                            <input type="text" class="form-control form-control-sm" id="databaseToAppAppSearchInput" placeholder="搜索应用...">
                                        </div>
                                        <div class="dropdown-options" id="databaseToAppAppOptions">
                                            <div class="dropdown-option" data-value="" data-text="请选择应用">请选择应用</div>
                                            <div class="dropdown-option" data-value="" data-text="取消分配">取消分配</div>
                                            <!-- 应用选项将通过JavaScript动态加载，过滤已分配的应用 -->
                                        </div>
                                    </div>
                                </div>
                                <div class="form-text">只显示已分配数据库的应用</div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>注意：</strong>选择"取消分配"将解除数据库与应用的绑定关系。
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveDatabaseToAppBinding()">确定绑定</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'rds_manager/js/searchable-dropdown.js' %}"></script>
<script>
let currentDatabaseId = null;

// 产品线数据（包含负责人信息）
const productLinesData = {
    {% for line in product_lines %}
    '{{ line.id }}': {
        'id': {{ line.id }},
        'name': '{{ line.name|escapejs }}',
        'biz_domain_id': {{ line.biz_domain_id }},
        'team_leader_name': '{{ line.team_leader_name|default:""|escapejs }}',
        'owner': '{{ line.owner|default:""|escapejs }}'
    }{% if not forloop.last %},{% endif %}
    {% endfor %}
};

// 根据产品线ID加载负责人信息
function loadProductLineOwner(productLineId) {
    console.log('加载产品线负责人信息，产品线ID:', productLineId);

    const productLine = productLinesData[productLineId];
    if (productLine) {
        const techOwnerField = document.getElementById('techOwner');
        if (techOwnerField) {
            // 优先使用 team_leader_name，如果没有则使用 owner
            const ownerName = productLine.team_leader_name || productLine.owner || '';
            if (ownerName && (!techOwnerField.value || techOwnerField.value.trim() === '')) {
                techOwnerField.value = ownerName;
                console.log('自动填充负责人:', ownerName);
            } else {
                console.log('负责人字段已有值或产品线无负责人信息');
            }
        }
    } else {
        console.log('未找到产品线数据:', productLineId);
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    initializeAppDropdown();
    initializeInstanceDropdown();
    initializeBusinessDomainDropdown();
    initializeFilterDropdowns();
});

let appDropdown = null;

// 初始化应用下拉框
function initializeAppDropdown() {
    appDropdown = initializeSearchableDropdown({
        dropdownId: 'appDropdown',
        searchInputId: 'appSearch',
        hiddenInputId: 'appSelect',
        filterInputId: 'appSearchInput',
        optionsContainerId: 'appOptions',
        placeholder: '未分配',
        searchPlaceholder: '搜索应用...',
        onChange: function(value, text) {
            console.log('应用选择变更:', value, text);
            // 更新业务线和负责人信息
            updateAppInfo();
        }
    });
}

// 设置应用下拉框的值
function setAppDropdownValue(appId) {
    if (appDropdown) {
        appDropdown.setValue(appId || '');
    }
}

// 初始化实例搜索框
function initializeInstanceDropdown() {
    const instanceDropdown = initializeSearchableDropdown({
        dropdownId: 'instanceDropdown',
        searchInputId: 'instanceSearch',
        hiddenInputId: 'instance',
        filterInputId: 'instanceSearchInput',
        optionsContainerId: 'instanceOptions',
        placeholder: '全部实例',
        searchPlaceholder: '搜索实例...',
        onChange: function(value, text) {
            console.log('实例选择变更:', value, text);
        }
    });
}

let businessDomainDropdown = null;

// 初始化业务线搜索框
function initializeBusinessDomainDropdown() {
    businessDomainDropdown = initializeSearchableDropdown({
        dropdownId: 'businessDomainDropdown',
        searchInputId: 'businessDomainSearch',
        hiddenInputId: 'businessDomain',
        filterInputId: 'businessDomainSearchInput',
        optionsContainerId: 'businessDomainOptions',
        placeholder: '未分配',
        searchPlaceholder: '搜索业务线...',
        onChange: function(value, text) {
            console.log('业务线选择变更:', value, text);
            // 当选择产品线时，自动加载负责人信息
            if (value && value !== 'null') {
                loadProductLineOwner(value);
            }
        }
    });
}

// 初始化过滤器下拉框
function initializeFilterDropdowns() {
    // 初始化业务域过滤器
    const bizDomainFilterDropdown = initializeSearchableDropdown({
        dropdownId: 'bizDomainDropdown',
        searchInputId: 'bizDomainSearch',
        hiddenInputId: 'bizDomain',
        filterInputId: 'bizDomainSearchInput',
        optionsContainerId: 'bizDomainOptions',
        placeholder: '全部业务域',
        searchPlaceholder: '搜索业务域...',
        onChange: function(value, text) {
            console.log('业务域过滤变更:', value, text);
            updateProductLineFilter(value);
        }
    });

    // 初始化产品线过滤器
    const productLineFilterDropdown = initializeSearchableDropdown({
        dropdownId: 'productLineDropdown',
        searchInputId: 'productLineSearch',
        hiddenInputId: 'productLine',
        filterInputId: 'productLineSearchInput',
        optionsContainerId: 'productLineOptions',
        placeholder: '全部产品线',
        searchPlaceholder: '搜索产品线...',
        onChange: function(value, text) {
            console.log('产品线过滤变更:', value, text);
            updateApplicationFilter(value);
        }
    });

    // 初始化应用过滤器
    const applicationFilterDropdown = initializeSearchableDropdown({
        dropdownId: 'applicationDropdown',
        searchInputId: 'applicationSearch',
        hiddenInputId: 'application',
        filterInputId: 'applicationSearchInput',
        optionsContainerId: 'applicationOptions',
        placeholder: '全部应用',
        searchPlaceholder: '搜索应用...',
        onChange: function(value, text) {
            console.log('应用过滤变更:', value, text);
        }
    });
}

// 更新产品线过滤器（根据业务域）
function updateProductLineFilter(bizDomainId) {
    const productLineOptions = $('#productLineOptions');

    // 清空产品线选项，保留"全部产品线"选项
    productLineOptions.html('<div class="dropdown-option" data-value="" data-text="全部产品线">全部产品线</div>');

    if (bizDomainId) {
        // 根据业务域过滤产品线
        $('#productLineOptions .dropdown-option[data-domain]').each(function() {
            const option = $(this);
            if (option.data('domain') == bizDomainId) {
                option.show();
            } else {
                option.hide();
            }
        });

        // 重新添加过滤后的产品线选项
        Object.values(productLinesData).forEach(line => {
            if (line.biz_domain_id.toString() === bizDomainId) {
                productLineOptions.append(
                    `<div class="dropdown-option" data-value="${line.id}" data-text="${line.name}" data-domain="${line.biz_domain_id}">${line.name}</div>`
                );
            }
        });
    } else {
        // 显示所有产品线
        Object.values(productLinesData).forEach(line => {
            productLineOptions.append(
                `<div class="dropdown-option" data-value="${line.id}" data-text="${line.name}" data-domain="${line.biz_domain_id}">${line.name}</div>`
            );
        });
    }

    // 重置产品线选择
    const productLineDropdown = $('#productLineSearch');
    productLineDropdown.val('全部产品线');
    $('#productLine').val('');

    // 同时重置应用选择
    updateApplicationFilter('');
}

// 更新应用过滤器（根据产品线）
function updateApplicationFilter(productLineId) {
    const applicationOptions = $('#applicationOptions');

    // 清空应用选项，保留"全部应用"和"未分配"选项
    applicationOptions.html('<div class="dropdown-option" data-value="" data-text="全部应用">全部应用</div><div class="dropdown-option" data-value="unassigned" data-text="未分配">未分配</div>');

    if (productLineId) {
        // 根据产品线过滤应用
        applicationsData.forEach(app => {
            if (app.product_line_id && app.product_line_id.toString() === productLineId) {
                applicationOptions.append(
                    `<div class="dropdown-option" data-value="${app.id}" data-text="${app.name}" data-product-line="${app.product_line_id}">${app.name}</div>`
                );
            }
        });
    } else {
        // 显示所有应用
        applicationsData.forEach(app => {
            applicationOptions.append(
                `<div class="dropdown-option" data-value="${app.id}" data-text="${app.name}" data-product-line="${app.product_line_id || ''}">${app.name}</div>`
            );
        });
    }

    // 重置应用选择
    const applicationDropdown = $('#applicationSearch');
    applicationDropdown.val('全部应用');
    $('#application').val('');
}

// 设置当前选中的过滤值
$(document).ready(function() {
    // 设置当前业务域选择
    const currentBizDomain = '{{ current_biz_domain }}';
    if (currentBizDomain) {
        setTimeout(function() {
            const bizDomainOption = $(`#bizDomainOptions .dropdown-option[data-value="${currentBizDomain}"]`);
            if (bizDomainOption.length) {
                $('#bizDomainSearch').val(bizDomainOption.data('text'));
                $('#bizDomain').val(currentBizDomain);
                updateProductLineFilter(currentBizDomain);
            }
        }, 100);
    }

    // 设置当前产品线选择
    const currentProductLine = '{{ current_product_line }}';
    if (currentProductLine) {
        setTimeout(function() {
            const productLineOption = $(`#productLineOptions .dropdown-option[data-value="${currentProductLine}"]`);
            if (productLineOption.length) {
                $('#productLineSearch').val(productLineOption.data('text'));
                $('#productLine').val(currentProductLine);
                updateApplicationFilter(currentProductLine);
            }
        }, 200);
    }

    // 设置当前应用选择
    const currentApplication = '{{ current_application }}';
    if (currentApplication) {
        setTimeout(function() {
            let applicationOption;
            if (currentApplication === 'unassigned') {
                applicationOption = $(`#applicationOptions .dropdown-option[data-value="unassigned"]`);
            } else {
                applicationOption = $(`#applicationOptions .dropdown-option[data-value="${currentApplication}"]`);
            }
            if (applicationOption.length) {
                $('#applicationSearch').val(applicationOption.data('text'));
                $('#application').val(currentApplication);
            }
        }, 300);
    }
});



function openEditModal(dbId, dbName, instanceId, instanceName, appId, productLineId, techOwner) {
    currentDatabaseId = dbId;

    document.getElementById('databaseId').value = dbId;
    document.getElementById('databaseName').value = dbName;
    document.getElementById('instanceId').value = instanceId;
    document.getElementById('instanceName').value = instanceName || '';

    // 设置应用选择
    const appSelect = document.getElementById('appSelect');
    appSelect.value = appId || '';

    // 设置应用搜索框显示值
    setAppDropdownValue(appId);

    // 确保业务线下拉框已初始化
    if (!businessDomainDropdown) {
        initializeBusinessDomainDropdown();
    }

    // 如果有应用ID，动态获取最新的应用信息
    if (appId && appId !== 'null') {
        console.log('动态获取应用信息，应用ID:', appId);
        fetch(`/rds/applications/${appId}/`, {
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const app = data.application;
                console.log('获取到最新应用信息:', app);

                // 设置产品线信息
                if (app.product_line_id) {
                    setProductLineValue(app.product_line_id);
                } else {
                    setProductLineValue(null);
                }

                // 设置负责人信息
                const techOwnerField = document.getElementById('techOwner');
                const latestTechOwner = app.tech_owner ? app.tech_owner.username : '';
                techOwnerField.value = latestTechOwner;
                console.log('设置最新负责人:', latestTechOwner);
            } else {
                // 如果获取失败，使用传入的参数
                setProductLineValue(productLineId);
                document.getElementById('techOwner').value = techOwner || '';
            }
        })
        .catch(error => {
            console.error('获取应用信息失败:', error);
            // 如果获取失败，使用传入的参数
            setProductLineValue(productLineId);
            document.getElementById('techOwner').value = techOwner || '';
        });
    } else {
        // 没有应用ID，使用传入的参数
        setProductLineValue(productLineId);
        document.getElementById('techOwner').value = techOwner || '';
    }

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editModal'));
    modal.show();
}

// 独立的设置产品线值的函数
function setProductLineValue(productLineId) {
    console.log('设置产品线ID:', productLineId);
    console.log('产品线ID类型:', typeof productLineId);

    // 首先清空当前值，避免全局污染
    document.getElementById('businessDomain').value = '';
    document.getElementById('businessDomainSearch').value = '';

    const businessDomainDropdown = document.getElementById('businessDomainDropdown');

    if (businessDomainDropdown && productLineId && productLineId !== 'null') {
        // 确保productLineId转换为字符串进行比较
        const productLineIdStr = String(productLineId);
        const option = document.querySelector(`#businessDomainOptions .dropdown-option[data-value="${productLineIdStr}"]`);
        console.log('查找选项结果:', option, '查找的ID:', productLineIdStr);

        if (option) {
            const productLineName = option.getAttribute('data-text');
            // 设置隐藏字段的值
            document.getElementById('businessDomain').value = productLineId;
            // 设置显示值
            document.getElementById('businessDomainSearch').value = productLineName;
            console.log('设置产品线显示值:', productLineName);
        } else {
            console.log('未找到产品线选项:', productLineIdStr);
            // 列出所有可用选项进行调试
            const allOptions = document.querySelectorAll('#businessDomainOptions .dropdown-option');
            console.log('所有可用的产品线选项:');
            allOptions.forEach(opt => {
                console.log(`- data-value: "${opt.getAttribute('data-value')}", data-text: "${opt.getAttribute('data-text')}"`);
            });
            document.getElementById('businessDomainSearch').value = '未分配';
        }
    } else {
        // 如果没有产品线ID，设置为未分配
        document.getElementById('businessDomainSearch').value = '未分配';
        console.log('产品线ID为空，设置为未分配');
    }
}

function updateAppInfo() {
    const appSelect = document.getElementById('appSelect');
    const selectedAppId = appSelect.value;

    if (selectedAppId) {
        // 通过AJAX获取应用详细信息
        fetch(`/rds/applications/${selectedAppId}/`, {
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const app = data.application;
                    console.log('获取到应用信息:', app);

                    // 自动填充产品线（如果应用有产品线信息）
                    if (app.product_line_id) {
                        console.log('应用的产品线ID:', app.product_line_id);
                        setProductLineValue(app.product_line_id);

                        // 从产品线获取负责人信息
                        const techOwnerField = document.getElementById('techOwner');
                        if (!techOwnerField.value || techOwnerField.value.trim() === '') {
                            // 从产品线信息中获取负责人
                            const techOwner = app.tech_owner ? app.tech_owner.username : '';
                            techOwnerField.value = techOwner;
                            console.log('从产品线信息设置负责人:', techOwner);
                        } else {
                            console.log('保持现有负责人值:', techOwnerField.value);
                        }
                    }
                } else {
                    // 如果获取失败，只在字段为空时清空负责人
                    const techOwnerField = document.getElementById('techOwner');
                    if (!techOwnerField.value) {
                        techOwnerField.value = '';
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // 出错时不清空用户已输入的内容
            });
    } else {
        // 当选择"未分配"时，只在字段为空时才清空负责人
        const techOwnerField = document.getElementById('techOwner');
        if (!techOwnerField.value) {
            techOwnerField.value = '';
        }
    }
}

function saveAssignment() {
    const formData = new FormData(document.getElementById('editForm'));
    
    fetch('{% url "rds_manager:assign_database_to_app" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': '{{ csrf_token }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新表格行
            updateTableRow(data.database);
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
            modal.hide();
            
            // 显示成功消息
            showMessage(data.message, 'success');
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('操作失败，请重试', 'error');
    });
}

function updateTableRow(database) {
    const row = document.getElementById(`db-row-${database.id}`);
    if (row) {
        // 更新应用列
        const appCell = document.getElementById(`app-cell-${database.id}`);
        if (database.app_name) {
            appCell.innerHTML = `<span class="app-badge">${database.app_name}</span>`;
        } else {
            appCell.innerHTML = `<span class="unassigned-badge">未分配</span>`;
        }

        // 更新业务线列
        const domainCell = document.getElementById(`domain-cell-${database.id}`);
        if (database.product_line_name) {
            domainCell.innerHTML = `<span class="text-success">${database.product_line_name}</span>`;
        } else {
            domainCell.innerHTML = `<span class="text-muted">-</span>`;
        }

        // 更新负责人列
        const ownerCell = document.getElementById(`owner-cell-${database.id}`);
        if (database.tech_owner) {
            ownerCell.innerHTML = `<span class="text-primary">${database.tech_owner}</span>`;
        } else {
            ownerCell.innerHTML = `<span class="text-muted">-</span>`;
        }

        // 更新编辑按钮的onclick参数
        const editBtn = row.querySelector('.edit-btn');
        if (editBtn) {
            const instanceName = database.instance_name || database.instance_id;
            const appId = database.app_id || 'null';
            const productLineId = database.product_line_id || 'null';
            const techOwner = database.tech_owner || '';

            editBtn.setAttribute('onclick',
                `openEditModal(${database.id}, '${database.name}', '${database.instance_id}', '${instanceName}', ${appId}, ${productLineId}, '${techOwner}')`
            );
        }
    }
}

function showMessage(message, type) {
    // 简单的消息提示
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部插入消息
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}

// 应用选择变化时更新信息
document.getElementById('appSelect').addEventListener('change', updateAppInfo);

// 打开应用绑定到产品线模态框
function openAppToProductLineModal() {
    console.log('openAppToProductLineModal called');

    try {
        // 显示模态框
        const modalElement = document.getElementById('appToProductLineModal');
        if (!modalElement) {
            console.error('AppToProductLine modal element not found');
            return;
        }

        const modal = new bootstrap.Modal(modalElement);

        // 监听模态框显示完成事件
        modalElement.addEventListener('shown.bs.modal', function() {
            console.log('AppToProductLine modal shown, initializing dropdowns...');
            initializeAppToProductLineDropdowns();
        }, { once: true });

        modal.show();

    } catch (error) {
        console.error('Error opening AppToProductLine modal:', error);
    }
}

// 初始化应用绑定到产品线的下拉框
function initializeAppToProductLineDropdowns() {
    try {
        console.log('Initializing AppToProductLine dropdowns...');

        // 检查 SearchableDropdown 类是否可用
        if (typeof SearchableDropdown === 'undefined') {
            console.error('SearchableDropdown class not found');
            return;
        }

        // 强制重新创建SearchableDropdown组件
        console.log('Current appToProductLineAppDropdown:', window.appToProductLineAppDropdown);
        console.log('Type:', typeof window.appToProductLineAppDropdown);
        if (window.appToProductLineAppDropdown) {
            console.log('getValue method exists:', typeof window.appToProductLineAppDropdown.getValue);
        }

        console.log('Force creating new appToProductLineAppDropdown...');
        try {
            window.appToProductLineAppDropdown = new SearchableDropdown({
                dropdownId: 'appToProductLineAppDropdown',
                searchInputId: 'appToProductLineAppSearch',
                hiddenInputId: 'appToProductLineAppSelect',
                filterInputId: 'appToProductLineAppSearchInput',
                optionsContainerId: 'appToProductLineAppOptions',
                placeholder: '请选择应用',
                searchPlaceholder: '搜索应用...'
            });
            console.log('appToProductLineAppDropdown created successfully:', window.appToProductLineAppDropdown);
            console.log('getValue method exists:', typeof window.appToProductLineAppDropdown.getValue);
        } catch (error) {
            console.error('Failed to create appToProductLineAppDropdown:', error);
            return;
        }

        console.log('Force creating new appToProductLineProductLineDropdown...');
        try {
            window.appToProductLineProductLineDropdown = new SearchableDropdown({
                dropdownId: 'appToProductLineProductLineDropdown',
                searchInputId: 'appToProductLineProductLineSearch',
                hiddenInputId: 'appToProductLineProductLineSelect',
                filterInputId: 'appToProductLineProductLineSearchInput',
                optionsContainerId: 'appToProductLineProductLineOptions',
                placeholder: '请选择产品线',
                searchPlaceholder: '搜索产品线...'
            });
            console.log('appToProductLineProductLineDropdown created successfully:', window.appToProductLineProductLineDropdown);
            console.log('getValue method exists:', typeof window.appToProductLineProductLineDropdown.getValue);
        } catch (error) {
            console.error('Failed to create appToProductLineProductLineDropdown:', error);
            return;
        }

        console.log('AppToProductLine dropdowns initialized successfully');

    } catch (error) {
        console.error('Error initializing AppToProductLine dropdowns:', error);
    }
}

// 打开数据库绑定到应用模态框
function openDatabaseToAppModal() {
    console.log('openDatabaseToAppModal called');

    try {
        // 显示模态框
        const modalElement = document.getElementById('databaseToAppModal');
        if (!modalElement) {
            console.error('Modal element not found');
            return;
        }

        const modal = new bootstrap.Modal(modalElement);

        // 监听模态框显示完成事件
        modalElement.addEventListener('shown.bs.modal', function() {
            console.log('Modal shown, initializing dropdowns...');
            initializeDatabaseToAppDropdowns();
        }, { once: true });

        modal.show();

    } catch (error) {
        console.error('Error opening modal:', error);
    }
}

// 初始化数据库绑定到应用的下拉框
function initializeDatabaseToAppDropdowns() {
    try {
        console.log('Initializing DatabaseToApp dropdowns...');

        // 检查 SearchableDropdown 类是否可用
        if (typeof SearchableDropdown === 'undefined') {
            console.error('SearchableDropdown class not found');
            return;
        }
        console.log('SearchableDropdown class found:', SearchableDropdown);

        // 禁用旧的SearchableDropdown，使用新的简单实现
        console.log('Using simple dropdown implementation instead of SearchableDropdown');

        // 清除可能存在的旧实例
        window.databaseToAppDatabaseDropdown = null;
        window.databaseToAppAppDropdown = null;

        // 加载已分配数据库的应用
        loadAssignedAppsForBinding();

        console.log('DatabaseToApp dropdowns initialized successfully');



        // 通用的简单下拉框实现
        window.initAllSimpleDropdowns = function() {
            console.log('Initializing all simple dropdowns...');

            // 获取所有下拉框容器
            const dropdowns = document.querySelectorAll('.searchable-dropdown');

            dropdowns.forEach(dropdown => {
                const input = dropdown.querySelector('.dropdown-search[readonly]');
                const searchInput = dropdown.querySelector('.form-control-sm');
                const hiddenInput = dropdown.querySelector('input[type="hidden"]');
                const menu = dropdown.querySelector('.dropdown-menu-custom');

                if (!input || !searchInput || !hiddenInput || !menu) {
                    console.log('Skipping dropdown - missing elements:', dropdown.id, {
                        input: !!input,
                        searchInput: !!searchInput,
                        hiddenInput: !!hiddenInput,
                        menu: !!menu
                    });
                    return;
                }

                console.log('Initializing dropdown:', dropdown.id);

                // 点击输入框切换下拉菜单
                input.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // 关闭其他所有下拉框
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            otherDropdown.classList.remove('open');
                        }
                    });

                    // 切换当前下拉框
                    dropdown.classList.toggle('open');
                    if (dropdown.classList.contains('open')) {
                        searchInput.focus();
                    }
                });

                // 搜索功能
                searchInput.addEventListener('input', function(e) {
                    const term = e.target.value.toLowerCase();
                    const options = dropdown.querySelectorAll('.dropdown-option');
                    options.forEach(option => {
                        const text = option.textContent.toLowerCase();
                        if (text.includes(term)) {
                            option.style.display = 'block';
                        } else {
                            option.style.display = 'none';
                        }
                    });
                });

                // 选项点击
                dropdown.addEventListener('click', function(e) {
                    if (e.target.classList.contains('dropdown-option')) {
                        const value = e.target.getAttribute('data-value');
                        const text = e.target.getAttribute('data-text') || e.target.textContent;

                        input.value = text;
                        hiddenInput.value = value;
                        dropdown.classList.remove('open');

                        console.log('Selected:', {dropdown: dropdown.id, value, text});
                    }
                });
            });

            // 全局点击外部关闭所有下拉框
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.searchable-dropdown')) {
                    dropdowns.forEach(dropdown => {
                        dropdown.classList.remove('open');
                    });
                }
            });

            console.log('All simple dropdowns initialized successfully');
        };

        // 使用简单实现替换复杂的SearchableDropdown
        window.initAllSimpleDropdowns();

    } catch (error) {
        console.error('Error initializing dropdowns:', error);
    }
}

// 预定义数据（使用所有数据库，不是分页后的）
const databasesData = [
    {% for db in all_databases %}
    {
        id: {{ db.id }},
        app_id: {% if db.app_id %}{{ db.app_id }}{% else %}null{% endif %}
    }{% if not forloop.last %},{% endif %}
    {% endfor %}
];

const applicationsData = [
    {% for app in applications %}
    {
        id: {{ app.id }},
        name: '{{ app.name|escapejs }}',
        product_line_id: {% if app.product_line_id %}{{ app.product_line_id }}{% else %}null{% endif %}
    }{% if not forloop.last %},{% endif %}
    {% endfor %}
];



// 加载已分配数据库的应用
function loadAssignedAppsForBinding() {
    const appOptionsContainer = document.getElementById('databaseToAppAppOptions');

    if (!appOptionsContainer) {
        console.error('databaseToAppAppOptions container not found');
        return;
    }



    // 获取所有已分配数据库的应用ID
    const assignedAppIds = new Set();

    // 从当前页面的数据库列表中收集已分配的应用ID
    databasesData.forEach(db => {
        if (db.app_id) {
            assignedAppIds.add(db.app_id);
        }
    });



    // 重新构建应用选项
    appOptionsContainer.innerHTML = '<div class="dropdown-option" data-value="" data-text="请选择应用">请选择应用</div><div class="dropdown-option" data-value="" data-text="取消分配">取消分配</div>';

    // 添加已分配数据库的应用
    let addedCount = 0;
    applicationsData.forEach(app => {
        if (assignedAppIds.has(app.id)) {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'dropdown-option';
            optionDiv.setAttribute('data-value', app.id.toString());
            optionDiv.setAttribute('data-text', app.name);
            optionDiv.textContent = app.name;
            appOptionsContainer.appendChild(optionDiv);
            addedCount++;

        }
    });



    // 注意：不需要重新绑定事件，因为事件已经绑定在容器上
    // SearchableDropdown 的事件监听器会自动处理动态添加的选项
}

// 保存应用绑定到产品线
function saveAppToProductLineBinding() {
    // 确保下拉框已初始化
    if (!window.appToProductLineAppDropdown || !window.appToProductLineProductLineDropdown) {
        console.error('下拉框未初始化，尝试重新初始化...');
        initializeAppToProductLineDropdowns();
        // 给一点时间让初始化完成
        setTimeout(() => {
            saveAppToProductLineBinding();
        }, 100);
        return;
    }

    const appId = window.appToProductLineAppDropdown.getValue();
    const productLineId = window.appToProductLineProductLineDropdown.getValue();

    if (!appId || !productLineId) {
        showMessage('请选择应用和产品线', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('app_id', appId);
    formData.append('product_line_id', productLineId);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch('/rds/bind-product-line/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('appToProductLineModal'));
            modal.hide();

            // 显示成功消息
            showMessage(data.message, 'success');

            // 刷新页面以显示更新后的数据
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('操作失败，请重试', 'error');
    });
}

// 保存数据库绑定到应用
function saveDatabaseToAppBinding() {
    // 直接从DOM元素获取值，不依赖SearchableDropdown实例
    const databaseId = document.getElementById('databaseToAppDatabaseSelect').value;
    const appId = document.getElementById('databaseToAppAppSelect').value;

    if (!databaseId) {
        showMessage('请选择数据库', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('database_id', databaseId);
    formData.append('app_id', appId); // 可以为空，表示取消分配
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch('/rds/assign-database/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('databaseToAppModal'));
            modal.hide();

            // 显示成功消息
            showMessage(data.message, 'success');

            // 刷新页面以显示更新后的数据
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('操作失败，请重试', 'error');
    });
}

</script>
{% endblock %}
