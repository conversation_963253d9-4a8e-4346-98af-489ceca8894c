{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}应用管理仪表盘{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .stats-card h3 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: bold;
    }
    .stats-card p {
        margin: 5px 0 0 0;
        opacity: 0.9;
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .domain-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
    .domain-item:last-child {
        border-bottom: none;
    }
    .domain-name {
        font-weight: 500;
        color: #333;
    }
    .app-count {
        background: #007bff;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-cubes"></i> 应用管理仪表盘
            </h1>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row">
        <div class="col-md-3">
            <div class="stats-card">
                <h3>{{ biz_domain_count }}</h3>
                <p><i class="fas fa-building"></i> 业务域</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <h3>{{ product_line_count }}</h3>
                <p><i class="fas fa-layer-group"></i> 产品线</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <h3>{{ app_count }}</h3>
                <p><i class="fas fa-cube"></i> 应用系统</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <h3>{{ db_relation_count }}</h3>
                <p><i class="fas fa-link"></i> 数据库关联</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 业务域产品线统计 -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-building"></i> 业务域产品线分布 (Top 10)</h5>
                {% if domain_stats %}
                    {% for domain in domain_stats %}
                    <div class="domain-item">
                        <span class="domain-name">{{ domain.name }}</span>
                        <span class="app-count">{{ domain.product_line_count }} 个产品线</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">暂无数据</p>
                {% endif %}
            </div>
        </div>

        <!-- 应用类型统计 -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-chart-pie"></i> 应用类型分布</h5>
                {% if app_type_stats %}
                    <canvas id="appTypeChart" width="400" height="200"></canvas>
                {% else %}
                    <p class="text-muted">暂无数据</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 环境分布 -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-server"></i> 环境分布</h5>
                {% if env_stats %}
                    <canvas id="envChart" width="400" height="200"></canvas>
                {% else %}
                    <p class="text-muted">暂无数据</p>
                {% endif %}
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="col-md-6">
            <div class="chart-container">
                <h5><i class="fas fa-tools"></i> 快速操作</h5>
                <div class="list-group">
                    <a href="{% url 'rds_manager:business_domain_list' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-building"></i> 管理业务域和产品线
                    </a>
                    <a href="{% url 'rds_manager:application_list' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-cube"></i> 管理应用系统
                    </a>
                    <a href="{% url 'rds_manager:database_assignment' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-link"></i> 管理数据库分配
                    </a>
                    <a href="/admin/rds_manager/businessdomain/add/" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus"></i> 新增业务域
                    </a>
                    <a href="/admin/rds_manager/application/add/" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus"></i> 新增应用
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 应用类型图表
{% if app_type_stats %}
const appTypeCtx = document.getElementById('appTypeChart').getContext('2d');
const appTypeChart = new Chart(appTypeCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for stat in app_type_stats %}
            '{{ stat.get_app_type_display|default:stat.app_type }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for stat in app_type_stats %}
                {{ stat.count }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

// 环境分布图表
{% if env_stats %}
const envCtx = document.getElementById('envChart').getContext('2d');
const envChart = new Chart(envCtx, {
    type: 'bar',
    data: {
        labels: [
            {% for stat in env_stats %}
            '{{ stat.get_environment_display|default:stat.environment }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '应用数量',
            data: [
                {% for stat in env_stats %}
                {{ stat.count }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: '#36A2EB',
            borderColor: '#36A2EB',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}
