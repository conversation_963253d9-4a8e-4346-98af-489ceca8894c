{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}{{ application.name }} - 应用详情{% endblock %}

{% block extra_css %}
<style>
    .app-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 30px;
        margin-bottom: 30px;
    }
    .info-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
    .info-item:last-child {
        border-bottom: none;
    }
    .info-label {
        font-weight: 500;
        color: #666;
    }
    .info-value {
        color: #333;
    }
    .db-card {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
    }
    .db-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    .db-title {
        font-weight: 600;
        color: #333;
    }
    .relation-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    .relation-primary { background: #d4edda; color: #155724; }
    .relation-secondary { background: #cce7ff; color: #004085; }
    .relation-cache { background: #fff3cd; color: #856404; }
    .relation-analytics { background: #f3e5f5; color: #4a148c; }
    .relation-backup { background: #f8d7da; color: #721c24; }
    .relation-temp { background: #f5f5f5; color: #424242; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 应用头部信息 -->
    <div class="app-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-cube"></i> {{ application.name }}
                </h1>
                <p class="mb-2">
                    <code>{{ application.code }}</code>
                    {% if application.version %}
                    <span class="badge bg-light text-dark ms-2">v{{ application.version }}</span>
                    {% endif %}
                </p>
                {% if application.description %}
                <p class="mb-0 opacity-75">{{ application.description }}</p>
                {% endif %}
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'rds_manager:application_list' %}" class="btn btn-light me-2">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
                <a href="/admin/rds_manager/application/{{ application.id }}/change/" class="btn btn-warning">
                    <i class="fas fa-edit"></i> 编辑应用
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 基本信息 -->
        <div class="col-md-6">
            <div class="info-card">
                <h5 class="mb-3"><i class="fas fa-info-circle"></i> 基本信息</h5>
                
                <div class="info-item">
                    <span class="info-label">应用名称</span>
                    <span class="info-value">{{ application.name }}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">应用代码</span>
                    <span class="info-value"><code>{{ application.code }}</code></span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">业务域</span>
                    <span class="info-value">
                        {% if application.business_domain %}
                            <span class="text-success">{{ application.business_domain.name }}</span>
                        {% else %}
                            <span class="text-muted">ID: {{ application.business_domain_id }}</span>
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">应用类型</span>
                    <span class="info-value">
                        <span class="badge bg-primary">{{ application.get_app_type_display }}</span>
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">环境</span>
                    <span class="info-value">
                        <span class="badge bg-info">{{ application.get_environment_display }}</span>
                    </span>
                </div>
                
                {% if application.version %}
                <div class="info-item">
                    <span class="info-label">版本号</span>
                    <span class="info-value">{{ application.version }}</span>
                </div>
                {% endif %}
                
                <div class="info-item">
                    <span class="info-label">创建时间</span>
                    <span class="info-value">{{ application.created_at|date:"Y-m-d H:i:s" }}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">更新时间</span>
                    <span class="info-value">{{ application.updated_at|date:"Y-m-d H:i:s" }}</span>
                </div>
            </div>
        </div>

        <!-- 负责人信息 -->
        <div class="col-md-6">
            <div class="info-card">
                <h5 class="mb-3"><i class="fas fa-users"></i> 负责人信息</h5>
                
                <div class="info-item">
                    <span class="info-label">技术负责人</span>
                    <span class="info-value">
                        {% if application.product_line and application.product_line.team_leader_display %}
                            <span class="text-primary">{{ application.product_line.team_leader_display }}</span>
                        {% else %}
                            <span class="text-muted">未指定</span>
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">业务负责人</span>
                    <span class="info-value">
                        {% if application.business_owner %}
                            <span class="text-success">{{ application.business_owner.username }}</span>
                        {% else %}
                            <span class="text-muted">未指定</span>
                        {% endif %}
                    </span>
                </div>
                
                {% if application.contact_email %}
                <div class="info-item">
                    <span class="info-label">联系邮箱</span>
                    <span class="info-value">
                        <a href="mailto:{{ application.contact_email }}">{{ application.contact_email }}</a>
                    </span>
                </div>
                {% endif %}
                
                {% if application.repository_url %}
                <div class="info-item">
                    <span class="info-label">代码仓库</span>
                    <span class="info-value">
                        <a href="{{ application.repository_url }}" target="_blank">
                            <i class="fas fa-external-link-alt"></i> 查看代码
                        </a>
                    </span>
                </div>
                {% endif %}
                
                {% if application.documentation_url %}
                <div class="info-item">
                    <span class="info-label">文档地址</span>
                    <span class="info-value">
                        <a href="{{ application.documentation_url }}" target="_blank">
                            <i class="fas fa-external-link-alt"></i> 查看文档
                        </a>
                    </span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 数据库关联 -->
    <div class="row">
        <div class="col-12">
            <div class="info-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-database"></i> 数据库分配 ({{ app_databases|length }})</h5>
                    <a href="{% url 'rds_manager:database_assignment' %}?app={{ application.id }}"
                       class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> 分配数据库
                    </a>
                </div>
                
                {% if app_databases %}
                    {% for app_db in app_databases %}
                    <div class="db-card">
                        <div class="db-header">
                            <div class="db-title">
                                <i class="fas fa-database"></i>
                                {{ app_db.instance_id }}.{{ app_db.name }}
                            </div>
                            <div>
                                <span class="badge bg-success">
                                    已分配
                                </span>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-server"></i> 实例:
                                    {% if app_db.instance %}
                                        {{ app_db.instance.instance_name }}
                                    {% else %}
                                        {{ app_db.instance_id }}
                                    {% endif %}
                                </small>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">
                                    <i class="fas fa-font"></i> 字符集: {{ app_db.character_set }}
                                </small>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> {{ app_db.created_at|date:"Y-m-d" }}
                                </small>
                            </div>
                        </div>
                        
                        {% if app_db.description %}
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-sticky-note"></i> {{ app_db.description }}
                            </small>
                        </div>
                        {% endif %}

                        <div class="mt-2">
                            <a href="/admin/rds_manager/database/{{ app_db.id }}/change/"
                               class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-database fa-2x text-muted mb-2"></i>
                        <p class="text-muted">该应用还没有分配任何数据库</p>
                        <a href="{% url 'rds_manager:database_assignment' %}"
                           class="btn btn-primary">
                            <i class="fas fa-plus"></i> 去数据库分配页面分配
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
