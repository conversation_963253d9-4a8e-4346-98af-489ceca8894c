{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}产品线管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'rds_manager/css/searchable-dropdown.css' %}">
<style>
    .search-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .product-line-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .view-tabs {
        background: white;
        border-radius: 10px 10px 0 0;
        padding: 15px 20px 0;
        margin-bottom: 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .view-tabs .nav-link {
        border: none;
        border-radius: 8px 8px 0 0;
        color: #6c757d;
        font-weight: 500;
    }
    .view-tabs .nav-link.active {
        background-color: #007bff;
        color: white;
    }
    .level-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    .level-1 {
        background: #d4edda;
        color: #155724;
    }
    .level-2 {
        background: #fff3cd;
        color: #856404;
    }
    .level-3 {
        background: #f8d7da;
        color: #721c24;
    }
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    .status-active {
        background: #d4edda;
        color: #155724;
    }
    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-layer-group"></i> 产品线管理
                </h1>
                <div>
                    <a href="{% url 'rds_manager:application_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line"></i> 返回仪表盘
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 视图切换标签 -->
    <div class="row">
        <div class="col-12">
            <div class="view-tabs">
                <ul class="nav nav-tabs" id="viewTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link {% if view_type == 'biz_domain' %}active{% endif %}" 
                           href="?view=biz_domain{% if search_query %}&search={{ search_query }}{% endif %}">
                            <i class="fas fa-building"></i> 业务域
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link {% if view_type == 'product_line' %}active{% endif %}" 
                           href="?view=product_line{% if search_query %}&search={{ search_query }}{% endif %}">
                            <i class="fas fa-layer-group"></i> 产品线
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="row">
        <div class="col-12">
            <div class="search-card">
                <form method="get" class="row g-3">
                    <input type="hidden" name="view" value="{{ view_type }}">
                    <div class="col-md-4">
                        <label for="search" class="form-label">搜索产品线</label>
                        <input type="text" name="search" id="search" class="form-control" 
                               value="{{ search_query }}" placeholder="输入产品线名称、负责人或SR专家">
                    </div>
                    <div class="col-md-3">
                        <label for="biz_domain" class="form-label">业务域</label>
                        <div class="searchable-dropdown" id="bizDomainDropdown">
                            <div class="dropdown-input-container">
                                <input type="text" id="bizDomainSearch" class="form-control dropdown-search" placeholder="请选择业务域" readonly>
                                <input type="hidden" id="biz_domain" name="biz_domain" value="{{ current_biz_domain }}">
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            <div class="dropdown-menu-custom" id="bizDomainDropdownMenu">
                                <div class="dropdown-search-container">
                                    <input type="text" class="form-control form-control-sm" placeholder="搜索业务域..." id="bizDomainSearchInput">
                                </div>
                                <div class="dropdown-options" id="bizDomainOptions">
                                    <div class="dropdown-option" data-value="" data-text="全部业务域">全部业务域</div>
                                    {% for domain in biz_domains %}
                                    <div class="dropdown-option" data-value="{{ domain.external_id }}" data-text="{{ domain.name }}">
                                        {{ domain.name }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="?view={{ view_type }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> 清除
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 产品线列表 -->
    {% if product_lines %}
    <div class="row">
        <div class="col-12">
            <div class="product-line-table">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>产品线名称</th>
                                <th>所属业务域</th>
                                <th>负责人</th>
                                <th>SR专家</th>
                                <th>线上Bug负责人</th>
                                <th>产品线等级</th>
                                <th>状态</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for pl in product_lines %}
                            <tr>
                                <td><code>{{ pl.external_id }}</code></td>
                                <td>
                                    <strong>{{ pl.name }}</strong>
                                </td>
                                <td>
                                    {% if pl.biz_domain %}
                                        <a href="?view=biz_domain&search={{ pl.biz_domain.name }}" class="text-primary">
                                            {{ pl.biz_domain.name }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">业务域ID: {{ pl.biz_domain_id }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if pl.owner %}
                                        <span class="text-primary">{{ pl.owner }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if pl.sr_expert %}
                                        <span class="text-success">{{ pl.sr_expert }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if pl.bug_online_owner %}
                                        <span class="text-warning">{{ pl.bug_online_owner }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if pl.product_line_level == 1 %}
                                        <span class="level-badge level-1">核心产品线</span>
                                    {% elif pl.product_line_level == 2 %}
                                        <span class="level-badge level-2">一般产品线</span>
                                    {% elif pl.product_line_level == 3 %}
                                        <span class="level-badge level-3">即将退市</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if pl.is_active and pl.listing_status %}
                                        <span class="status-badge status-active">正常</span>
                                    {% else %}
                                        <span class="status-badge status-inactive">停用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ pl.created_at|date:"Y-m-d H:i" }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="产品线分页">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_biz_domain %}&biz_domain={{ current_biz_domain }}{% endif %}">首页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_biz_domain %}&biz_domain={{ current_biz_domain }}{% endif %}">上一页</a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_biz_domain %}&biz_domain={{ current_biz_domain }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_biz_domain %}&biz_domain={{ current_biz_domain }}{% endif %}">下一页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_biz_domain %}&biz_domain={{ current_biz_domain }}{% endif %}">末页</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                <h4>暂无产品线</h4>
                <p class="text-muted">没有找到匹配的产品线，请调整搜索条件或运行应用同步脚本。</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'rds_manager/js/searchable-dropdown.js' %}"></script>
<script>
$(document).ready(function() {
    // 初始化业务域搜索框
    const bizDomainDropdown = initializeSearchableDropdown({
        dropdownId: 'bizDomainDropdown',
        searchInputId: 'bizDomainSearch',
        hiddenInputId: 'biz_domain',
        filterInputId: 'bizDomainSearchInput',
        optionsContainerId: 'bizDomainOptions',
        placeholder: '全部业务域',
        searchPlaceholder: '搜索业务域...',
        onChange: function(value, text) {
            console.log('业务域选择变更:', value, text);
        }
    });
});
</script>
{% endblock %}
