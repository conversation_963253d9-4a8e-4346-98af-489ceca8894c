{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}数据库运行情况看板 - 阿里云RDS管理平台{% endblock %}

{% block extra_css %}
<link href="{% static 'rds_manager/css/searchable-dropdown.css' %}" rel="stylesheet">
<style>
    .dashboard-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }
    .dashboard-card:hover {
        transform: translateY(-2px);
    }
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    .stat-card.sql-execution {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    .stat-card.slow-query {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .stat-card.risk-sql {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    .stat-card.table-stats {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 2rem;
    }
    .loading {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }
    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">{{ page_title }}</h1>
            <p class="text-muted">数据库表运行情况综合分析看板</p>
        </div>
    </div>

    <!-- 错误信息显示 -->
    {% if error_message %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                数据加载失败：{{ error_message }}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 过滤器区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filter-section">
                <h5 class="mb-3"><i class="fas fa-filter"></i> 数据过滤</h5>
                <form id="filterForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="bizDomainSelect" class="form-label">业务域</label>
                        <div class="searchable-dropdown" id="bizDomainDropdown">
                            <div class="dropdown-input-container">
                                <input type="text" id="bizDomainSearch" class="form-control dropdown-search" placeholder="全部业务域" readonly>
                                <input type="hidden" id="bizDomainSelect" name="biz_domain_id" value="">
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            <div class="dropdown-menu-custom">
                                <div class="dropdown-search-container">
                                    <input type="text" id="bizDomainSearchInput" placeholder="搜索业务域...">
                                </div>
                                <div class="dropdown-options" id="bizDomainOptions">
                                    <div class="dropdown-option" data-value="" data-text="全部业务域">全部业务域</div>
                                    {% for domain in business_domains %}
                                    <div class="dropdown-option" data-value="{{ domain.external_id }}" data-text="{{ domain.name }}">{{ domain.name }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="productLineSelect" class="form-label">产品线</label>
                        <div class="searchable-dropdown" id="productLineDropdown">
                            <div class="dropdown-input-container">
                                <input type="text" id="productLineSearch" class="form-control dropdown-search" placeholder="全部产品线" readonly>
                                <input type="hidden" id="productLineSelect" name="product_line_id" value="">
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            <div class="dropdown-menu-custom">
                                <div class="dropdown-search-container">
                                    <input type="text" id="productLineSearchInput" placeholder="搜索产品线...">
                                </div>
                                <div class="dropdown-options" id="productLineOptions">
                                    <div class="dropdown-option" data-value="" data-text="全部产品线">全部产品线</div>
                                    {% for line in product_lines %}
                                    <div class="dropdown-option" data-value="{{ line.external_id }}" data-text="{{ line.name }}" data-domain="{{ line.biz_domain_id }}">{{ line.name }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="startDate" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="startDate" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-2">
                        <label for="endDate" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="endDate" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-md-2">
                        <label for="periodSelect" class="form-label">聚合周期</label>
                        <select class="form-select" id="periodSelect" name="period">
                            <option value="day">按天</option>
                            <option value="week">按周</option>
                            <option value="month">按月</option>
                            <option value="quarter">按季度</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card sql-execution">
                <div class="stat-number" id="sqlExecutionCount">{{ total_stats.total_sql_execution|default:0 }}</div>
                <div class="stat-label">SQL执行总数</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card slow-query">
                <div class="stat-number" id="slowQueryCount">{{ total_stats.total_slow_queries|default:0 }}</div>
                <div class="stat-label">慢查询总数</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card risk-sql">
                <div class="stat-number" id="riskSqlCount">{{ total_stats.total_risk_sql|default:0 }}</div>
                <div class="stat-label">风险SQL总数</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card table-stats">
                <div class="stat-number" id="tableCount">{{ total_stats.total_tables|default:0 }}</div>
                <div class="stat-label">表总数</div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-line"></i> SQL执行趋势</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="sqlTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-bar"></i> 慢查询趋势</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="slowQueryTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-pie"></i> 风险SQL分布</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="riskSqlChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-trophy"></i> 业务域排行榜</h5>
                </div>
                <div class="card-body">
                    <div id="domainRanking">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-table"></i> 详细统计数据</h5>
                </div>
                <div class="card-body">
                    <div id="detailTable">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'rds_manager/js/searchable-dropdown.js' %}"></script>
<script>
$(document).ready(function() {
    // 全局变量
    let sqlTrendChart = null;
    let slowQueryTrendChart = null;
    let riskSqlChart = null;
    let bizDomainDropdown = null;
    let productLineDropdown = null;

    // 初始化
    initializeSearchableDropdowns();
    initializeCharts();
    loadDashboardData();

    // 绑定事件
    $('#startDate, #endDate, #periodSelect').change(function() {
        loadDashboardData();
    });

    // 初始化搜索框组件
    function initializeSearchableDropdowns() {
        // 初始化业务域下拉框
        bizDomainDropdown = initializeSearchableDropdown({
            dropdownId: 'bizDomainDropdown',
            searchInputId: 'bizDomainSearch',
            hiddenInputId: 'bizDomainSelect',
            filterInputId: 'bizDomainSearchInput',
            optionsContainerId: 'bizDomainOptions',
            placeholder: '全部业务域',
            searchPlaceholder: '搜索业务域...',
            onChange: function(value, text) {
                console.log('业务域选择变更:', value, text);
                updateProductLines();
                loadDashboardData();
            }
        });

        // 初始化产品线下拉框
        productLineDropdown = initializeSearchableDropdown({
            dropdownId: 'productLineDropdown',
            searchInputId: 'productLineSearch',
            hiddenInputId: 'productLineSelect',
            filterInputId: 'productLineSearchInput',
            optionsContainerId: 'productLineOptions',
            placeholder: '全部产品线',
            searchPlaceholder: '搜索产品线...',
            onChange: function(value, text) {
                console.log('产品线选择变更:', value, text);
                loadDashboardData();
            }
        });
    }

    // 初始化图表
    function initializeCharts() {
        // SQL执行趋势图
        const sqlTrendCtx = document.getElementById('sqlTrendChart').getContext('2d');
        sqlTrendChart = new Chart(sqlTrendCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'SQL执行数量',
                    data: [],
                    borderColor: '#4facfe',
                    backgroundColor: 'rgba(79, 172, 254, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 慢查询趋势图
        const slowQueryTrendCtx = document.getElementById('slowQueryTrendChart').getContext('2d');
        slowQueryTrendChart = new Chart(slowQueryTrendCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '慢查询数量',
                    data: [],
                    borderColor: '#f5576c',
                    backgroundColor: 'rgba(245, 87, 108, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 风险SQL分布图
        const riskSqlCtx = document.getElementById('riskSqlChart').getContext('2d');
        riskSqlChart = new Chart(riskSqlCtx, {
            type: 'doughnut',
            data: {
                labels: ['高风险', '中风险', '低风险'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: ['#dc3545', '#ffc107', '#28a745']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // 更新产品线下拉框
    function updateProductLines() {
        const bizDomainId = $('#bizDomainSelect').val();
        const productLineOptions = $('#productLineOptions');

        // 清空产品线选项，保留"全部产品线"选项
        productLineOptions.html('<div class="dropdown-option" data-value="" data-text="全部产品线">全部产品线</div>');

        if (bizDomainId) {
            // 根据业务域过滤产品线
            const allProductLines = [
                {% for line in product_lines %}
                {
                    external_id: '{{ line.external_id }}',
                    name: '{{ line.name }}',
                    biz_domain_id: '{{ line.biz_domain_id }}'
                },
                {% endfor %}
            ];

            const filteredLines = allProductLines.filter(line => line.biz_domain_id === bizDomainId);
            filteredLines.forEach(function(line) {
                productLineOptions.append(
                    `<div class="dropdown-option" data-value="${line.external_id}" data-text="${line.name}" data-domain="${line.biz_domain_id}">${line.name}</div>`
                );
            });
        } else {
            // 显示所有产品线
            {% for line in product_lines %}
            productLineOptions.append(
                '<div class="dropdown-option" data-value="{{ line.external_id }}" data-text="{{ line.name }}" data-domain="{{ line.biz_domain_id }}">{{ line.name }}</div>'
            );
            {% endfor %}
        }

        // 重置产品线选择
        if (productLineDropdown) {
            productLineDropdown.setValue('');
        }
    }

    // 加载看板数据
    function loadDashboardData() {
        const formData = {
            biz_domain_id: $('#bizDomainSelect').val(),
            product_line_id: $('#productLineSelect').val(),
            start_date: $('#startDate').val(),
            end_date: $('#endDate').val(),
            period: $('#periodSelect').val()
        };

        // 加载汇总数据
        loadSummaryData(formData);

        // 加载趋势数据
        loadTrendData(formData);

        // 加载排行榜数据
        loadRankingData(formData);

        // 加载详细表格数据
        loadDetailTableData(formData);
    }

    // 加载汇总数据
    function loadSummaryData(formData) {
        $.get('{% url "rds_manager:ajax_dashboard_summary" %}', formData)
        .done(function(response) {
            if (response.success && response.data.length > 0) {
                // 计算总计
                let totalSqlExecution = 0;
                let totalSlowQueries = 0;
                let totalHighRisk = 0;
                let totalMediumRisk = 0;
                let totalLowRisk = 0;
                let totalTables = 0;

                response.data.forEach(function(item) {
                    totalSqlExecution += item.total_sql_execution || 0;
                    totalSlowQueries += item.total_slow_queries || 0;
                    totalHighRisk += item.total_high_risk || 0;
                    totalMediumRisk += item.total_medium_risk || 0;
                    totalLowRisk += item.total_low_risk || 0;
                    totalTables += item.total_tables || 0;
                });

                // 更新统计卡片
                $('#sqlExecutionCount').text(totalSqlExecution.toLocaleString());
                $('#slowQueryCount').text(totalSlowQueries.toLocaleString());
                $('#riskSqlCount').text((totalHighRisk + totalMediumRisk + totalLowRisk).toLocaleString());
                $('#tableCount').text(totalTables.toLocaleString());

                // 更新风险SQL分布图
                riskSqlChart.data.datasets[0].data = [totalHighRisk, totalMediumRisk, totalLowRisk];
                riskSqlChart.update();
            }
        })
        .fail(function(xhr, status, error) {
            console.error('加载汇总数据失败:', error, xhr.responseText);
            if (xhr.status === 302) {
                showErrorMessage('请先登录系统');
                window.location.href = '/accounts/login/';
            } else {
                showErrorMessage('加载汇总数据失败，请稍后重试: ' + error);
            }
        });
    }

    // 加载趋势数据
    function loadTrendData(formData) {
        // SQL执行趋势
        $.get('{% url "rds_manager:ajax_dashboard_trend" %}', {
            ...formData,
            metric: 'sql_execution_count',
            days: 30
        }).done(function(response) {
            if (response.success) {
                const labels = response.data.map(item => item.snapshot_date);
                const data = response.data.map(item => item.value || 0);

                sqlTrendChart.data.labels = labels;
                sqlTrendChart.data.datasets[0].data = data;
                sqlTrendChart.update();
            }
        });

        // 慢查询趋势
        $.get('{% url "rds_manager:ajax_dashboard_trend" %}', {
            ...formData,
            metric: 'slow_query_count',
            days: 30
        }).done(function(response) {
            if (response.success) {
                const labels = response.data.map(item => item.snapshot_date);
                const data = response.data.map(item => item.value || 0);

                slowQueryTrendChart.data.labels = labels;
                slowQueryTrendChart.data.datasets[0].data = data;
                slowQueryTrendChart.update();
            }
        });
    }

    // 加载排行榜数据
    function loadRankingData(formData) {
        $.get('{% url "rds_manager:ajax_dashboard_ranking" %}', {
            ...formData,
            metric: 'sql_execution_count',
            limit: 10
        }).done(function(response) {
            if (response.success) {
                let html = '<div class="list-group">';
                response.data.forEach(function(item, index) {
                    const badgeClass = index < 3 ? 'bg-warning' : 'bg-secondary';
                    html += `
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge ${badgeClass} me-2">${index + 1}</span>
                                ${item.biz_domain_name}
                            </div>
                            <span class="badge bg-primary rounded-pill">${(item.total_value || 0).toLocaleString()}</span>
                        </div>
                    `;
                });
                html += '</div>';
                $('#domainRanking').html(html);
            }
        }).fail(function() {
            $('#domainRanking').html('<div class="text-danger">加载失败</div>');
        });
    }

    // 加载详细表格数据
    function loadDetailTableData(formData, page = 1, orderBy = 'period_date', orderDirection = 'desc') {
        const params = {
            ...formData,
            page: page,
            page_size: 20,
            order_by: orderBy,
            order_direction: orderDirection
        };

        $.get('{% url "rds_manager:ajax_dashboard_summary" %}', params)
        .done(function(response) {
            if (response.success) {
                let html = `
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="text-muted">共 ${response.count} 条记录，第 ${response.current_page}/${response.total_pages} 页</span>
                        </div>
                        <div>
                            <select class="form-select form-select-sm d-inline-block w-auto" id="tableSortSelect">
                                <option value="period_date:desc" ${orderBy === 'period_date' && orderDirection === 'desc' ? 'selected' : ''}>按日期降序</option>
                                <option value="period_date:asc" ${orderBy === 'period_date' && orderDirection === 'asc' ? 'selected' : ''}>按日期升序</option>
                                <option value="total_sql_execution:desc" ${orderBy === 'total_sql_execution' && orderDirection === 'desc' ? 'selected' : ''}>按SQL执行数降序</option>
                                <option value="total_slow_queries:desc" ${orderBy === 'total_slow_queries' && orderDirection === 'desc' ? 'selected' : ''}>按慢查询数降序</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>日期</th>
                                    <th>SQL执行数</th>
                                    <th>慢查询数</th>
                                    <th>高风险SQL</th>
                                    <th>中风险SQL</th>
                                    <th>低风险SQL</th>
                                    <th>表数量</th>
                                    <th>数据库数</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                if (response.data.length === 0) {
                    html += '<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>';
                } else {
                    response.data.forEach(function(item) {
                        html += `
                            <tr>
                                <td>${item.period_date || '-'}</td>
                                <td>${(item.total_sql_execution || 0).toLocaleString()}</td>
                                <td>${(item.total_slow_queries || 0).toLocaleString()}</td>
                                <td><span class="badge bg-danger">${item.total_high_risk || 0}</span></td>
                                <td><span class="badge bg-warning">${item.total_medium_risk || 0}</span></td>
                                <td><span class="badge bg-success">${item.total_low_risk || 0}</span></td>
                                <td>${(item.total_tables || 0).toLocaleString()}</td>
                                <td>${(item.total_databases || 0).toLocaleString()}</td>
                            </tr>
                        `;
                    });
                }

                html += '</tbody></table></div>';

                // 添加分页控件
                if (response.total_pages > 1) {
                    html += '<nav aria-label="分页导航"><ul class="pagination justify-content-center">';

                    // 上一页
                    if (response.current_page > 1) {
                        html += `<li class="page-item"><a class="page-link" href="#" data-page="${response.current_page - 1}">上一页</a></li>`;
                    }

                    // 页码
                    const startPage = Math.max(1, response.current_page - 2);
                    const endPage = Math.min(response.total_pages, response.current_page + 2);

                    for (let i = startPage; i <= endPage; i++) {
                        const activeClass = i === response.current_page ? 'active' : '';
                        html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
                    }

                    // 下一页
                    if (response.current_page < response.total_pages) {
                        html += `<li class="page-item"><a class="page-link" href="#" data-page="${response.current_page + 1}">下一页</a></li>`;
                    }

                    html += '</ul></nav>';
                }

                $('#detailTable').html(html);

                // 绑定分页事件
                $('#detailTable .page-link').click(function(e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    if (page) {
                        loadDetailTableData(getCurrentFormData(), page, orderBy, orderDirection);
                    }
                });

                // 绑定排序事件
                $('#tableSortSelect').change(function() {
                    const sortValue = $(this).val().split(':');
                    loadDetailTableData(getCurrentFormData(), 1, sortValue[0], sortValue[1]);
                });
            }
        })
        .fail(function() {
            $('#detailTable').html('<div class="text-danger">加载失败</div>');
        });
    }

    // 获取当前表单数据
    function getCurrentFormData() {
        return {
            biz_domain_id: $('#bizDomainSelect').val(),
            product_line_id: $('#productLineSelect').val(),
            start_date: $('#startDate').val(),
            end_date: $('#endDate').val(),
            period: $('#periodSelect').val()
        };
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container-fluid').prepend(alertHtml);

        // 5秒后自动消失
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container-fluid').prepend(alertHtml);

        // 3秒后自动消失
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }
});
</script>
{% endblock %}