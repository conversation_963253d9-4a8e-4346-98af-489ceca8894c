{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}应用系统管理{% endblock %}

{% block extra_css %}
<style>
    .filter-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .app-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .app-type-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    .env-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    .env-prod { background: #d4edda; color: #155724; }
    .env-staging { background: #fff3cd; color: #856404; }
    .env-test { background: #cce7ff; color: #004085; }
    .env-dev { background: #f8d7da; color: #721c24; }
    
    .type-web { background: #e7f3ff; color: #0056b3; }
    .type-api { background: #e8f5e8; color: #155724; }
    .type-batch { background: #fff3e0; color: #e65100; }
    .type-microservice { background: #f3e5f5; color: #4a148c; }
    .type-mobile { background: #e0f2f1; color: #00695c; }
    .type-desktop { background: #fce4ec; color: #880e4f; }
    .type-other { background: #f5f5f5; color: #424242; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-cube"></i> 应用系统管理
                </h1>
                <div>
                    <a href="{% url 'rds_manager:application_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line"></i> 返回仪表盘
                    </a>
                    <a href="/admin/rds_manager/application/add/" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 新增应用
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="row">
        <div class="col-12">
            <div class="filter-card">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="domain" class="form-label">业务域</label>
                        <select name="domain" id="domain" class="form-select">
                            <option value="">全部业务域</option>
                            {% for domain in domains %}
                            <option value="{{ domain.id }}" {% if current_domain == domain.id|stringformat:"s" %}selected{% endif %}>
                                {{ domain.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="type" class="form-label">应用类型</label>
                        <select name="type" id="type" class="form-select">
                            <option value="">全部类型</option>
                            {% for type_code, type_name in app_types %}
                            <option value="{{ type_code }}" {% if current_type == type_code %}selected{% endif %}>
                                {{ type_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="env" class="form-label">环境</label>
                        <select name="env" id="env" class="form-select">
                            <option value="">全部环境</option>
                            {% for env_code, env_name in environments %}
                            <option value="{{ env_code }}" {% if current_env == env_code %}selected{% endif %}>
                                {{ env_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 筛选
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 应用列表 -->
    {% if applications %}
    <div class="row">
        <div class="col-12">
            <div class="app-table">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>应用名称</th>
                                <th>应用代码</th>
                                <th>业务域</th>
                                <th>类型</th>
                                <th>环境</th>
                                <th>技术负责人</th>
                                <th>数据库数量</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for app in applications %}
                            <tr>
                                <td>
                                    <strong>{{ app.name }}</strong>
                                    {% if app.description %}
                                    <br><small class="text-muted">{{ app.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td><code>{{ app.code }}</code></td>
                                <td>
                                    {% if app.business_domain %}
                                        <span class="text-success">{{ app.business_domain.name }}</span>
                                    {% else %}
                                        <span class="text-muted">ID: {{ app.business_domain_id }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="app-type-badge type-{{ app.app_type }}">
                                        {{ app.get_app_type_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="env-badge env-{{ app.environment }}">
                                        {{ app.get_environment_display }}
                                    </span>
                                </td>
                                <td>
                                    {% if app.product_line and app.product_line.team_leader_display %}
                                        <span class="text-primary">{{ app.product_line.team_leader_display }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ app.db_count }}</span>
                                </td>
                                <td>{{ app.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'rds_manager:application_detail' app.id %}" 
                                           class="btn btn-outline-primary" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/admin/rds_manager/application/{{ app.id }}/change/" 
                                           class="btn btn-outline-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="应用分页">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if current_domain %}&domain={{ current_domain }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_env %}&env={{ current_env }}{% endif %}">首页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_domain %}&domain={{ current_domain }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_env %}&env={{ current_env }}{% endif %}">上一页</a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if current_domain %}&domain={{ current_domain }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_env %}&env={{ current_env }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_domain %}&domain={{ current_domain }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_env %}&env={{ current_env }}{% endif %}">下一页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_domain %}&domain={{ current_domain }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}{% if current_env %}&env={{ current_env }}{% endif %}">末页</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-cube fa-3x text-muted mb-3"></i>
                <h4>暂无应用系统</h4>
                <p class="text-muted">还没有创建任何应用系统，或者当前筛选条件下没有匹配的应用。</p>
                <a href="/admin/rds_manager/application/add/" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 创建第一个应用
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
