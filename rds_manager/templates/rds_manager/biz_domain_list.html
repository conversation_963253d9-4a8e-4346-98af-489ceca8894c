{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}业务域管理{% endblock %}

{% block extra_css %}
<style>
    .search-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .domain-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .view-tabs {
        background: white;
        border-radius: 10px 10px 0 0;
        padding: 15px 20px 0;
        margin-bottom: 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .view-tabs .nav-link {
        border: none;
        border-radius: 8px 8px 0 0;
        color: #6c757d;
        font-weight: 500;
    }
    .view-tabs .nav-link.active {
        background-color: #007bff;
        color: white;
    }
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    .status-active {
        background: #d4edda;
        color: #155724;
    }
    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }
    .count-badge {
        background: #e7f3ff;
        color: #0056b3;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-building"></i> 业务域管理
                </h1>
                <div>
                    <a href="{% url 'rds_manager:application_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line"></i> 返回仪表盘
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 视图切换标签 -->
    <div class="row">
        <div class="col-12">
            <div class="view-tabs">
                <ul class="nav nav-tabs" id="viewTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link {% if view_type == 'biz_domain' %}active{% endif %}" 
                           href="?view=biz_domain{% if search_query %}&search={{ search_query }}{% endif %}">
                            <i class="fas fa-building"></i> 业务域
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link {% if view_type == 'product_line' %}active{% endif %}" 
                           href="?view=product_line{% if search_query %}&search={{ search_query }}{% endif %}">
                            <i class="fas fa-layer-group"></i> 产品线
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="row">
        <div class="col-12">
            <div class="search-card">
                <form method="get" class="row g-3">
                    <input type="hidden" name="view" value="{{ view_type }}">
                    <div class="col-md-6">
                        <label for="search" class="form-label">搜索业务域</label>
                        <input type="text" name="search" id="search" class="form-control" 
                               value="{{ search_query }}" placeholder="输入业务域名称、负责人或技术负责人">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="?view={{ view_type }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> 清除
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 业务域列表 -->
    {% if biz_domains %}
    <div class="row">
        <div class="col-12">
            <div class="domain-table">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>业务域名称</th>
                                <th>负责人</th>
                                <th>技术负责人</th>
                                <th>PBU负责人</th>
                                <th>产品线数量</th>
                                <th>状态</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for domain in biz_domains %}
                            <tr>
                                <td><code>{{ domain.external_id }}</code></td>
                                <td>
                                    <strong>{{ domain.name }}</strong>
                                    {% if domain.description %}
                                        <br><small class="text-muted">{{ domain.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if domain.owner %}
                                        <span class="text-primary">{{ domain.owner }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if domain.tech_owner %}
                                        <span class="text-success">{{ domain.tech_owner }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if domain.pbu_owner %}
                                        <span class="text-info">{{ domain.pbu_owner }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="?view=product_line&biz_domain={{ domain.external_id }}" class="count-badge">
                                        {{ domain.product_line_count }} 个产品线
                                    </a>
                                </td>
                                <td>
                                    {% if domain.is_active and domain.listing_status %}
                                        <span class="status-badge status-active">正常</span>
                                    {% else %}
                                        <span class="status-badge status-inactive">停用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ domain.created_at|date:"Y-m-d H:i" }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="业务域分页">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}">首页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}">上一页</a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}">下一页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&view={{ view_type }}{% if search_query %}&search={{ search_query }}{% endif %}">末页</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}

    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h4>暂无业务域</h4>
                <p class="text-muted">没有找到匹配的业务域，请调整搜索条件或运行应用同步脚本。</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// 可以添加一些交互功能
</script>
{% endblock %}
