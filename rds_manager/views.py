from urllib import response
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db.models import Q, Count, Avg, Max, Min, Sum
from django.utils import timezone
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.contrib import messages
from django.core.paginator import Paginator
import json
import logging
from datetime import datetime, timedelta
from django.db.models.functions import TruncDate
import requests
from django.contrib.auth import login
import math

from .models import (
    RDSInstance, Database, DBAccount, DBAccountPrivilege,
    OperationLog, InstanceAccessPermission, AliCloudRegion, SlowQueryLog, SlowQueryAnalysis, SlowQueryScoreConfig,
    BusinessDomain, Application
)
from .services import AliCloudRDSService, InstanceSyncService, SlowQueryService, DashboardSnapshotService, DashboardQueryService
from accounts.models import User
from utils.logger import log_execution_time
from functools import wraps


def admin_required(view_func):
    """管理员权限检查装饰器"""
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_staff:
            # 如果是AJAX请求，返回JSON错误
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'success': False, 'error': '您没有权限访问此功能'})
            # 否则显示错误消息并重定向
            messages.error(request, '您没有权限访问此功能')
            return redirect('rds_manager:dashboard')
        return view_func(request, *args, **kwargs)
    return _wrapped_view


# 获取当前应用的logger
logger = logging.getLogger('rds_manager')

@login_required
def dashboard(request):
    """仪表盘视图"""
    # 统计数据
    instance_count = RDSInstance.objects.count()
    database_count = Database.objects.count()
    account_count = DBAccount.objects.count()
    log_count = OperationLog.objects.count()
    
    # 状态统计
    status_counts = RDSInstance.objects.values('status').annotate(count=Count('status'))
    status_data = {
        'labels': [status['status'] for status in status_counts],
        'data': [status['count'] for status in status_counts]
    }
    
    # 引擎统计
    engine_counts = RDSInstance.objects.values('engine').annotate(count=Count('engine'))
    engine_data = {
        'labels': [engine['engine'] for engine in engine_counts],
        'data': [engine['count'] for engine in engine_counts]
    }
    
    # 最近操作日志
    recent_logs = OperationLog.objects.all().order_by('-operation_time')[:10]
    
    context = {
        'instance_count': instance_count,
        'database_count': database_count,
        'account_count': account_count,
        'log_count': log_count,
        'status_data': json.dumps(status_data),
        'engine_data': json.dumps(engine_data),
        'recent_logs': recent_logs,
        'last_update': timezone.now()
    }
    
    return render(request, 'rds_manager/dashboard.html', context)

@login_required
def instance_list(request):
    """实例列表视图"""
    search = request.GET.get('search', '')
    region = request.GET.get('region', '')
    engine = request.GET.get('engine', '')
    status = request.GET.get('status', '')
    
    instances = RDSInstance.objects.all()
    
    # 应用过滤条件
    if search:
        instances = instances.filter(
            instance_id__icontains=search) | instances.filter(
            instance_name__icontains=search
        )
    
    if region:
        region_obj = AliCloudRegion.objects.filter(region_id=region).first()
        if region_obj:
            instances = instances.filter(region_id=region_obj.id)
    
    if engine:
        instances = instances.filter(engine=engine)
    
    if status:
        instances = instances.filter(status=status)
    
    # 获取过滤选项
    regions = AliCloudRegion.objects.all()
    engine_types = RDSInstance.objects.values_list('engine', flat=True).distinct()
    status_types = RDSInstance.objects.values_list('status', flat=True).distinct()
    
    context = {
        'instances': instances,
        'regions': regions,
        'engine_types': engine_types,
        'status_types': status_types
    }
    
    return render(request, 'rds_manager/instance_list.html', context)

@login_required
def instance_detail(request, instance_id):
    """RDS实例详情视图"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取该实例下的所有数据库
    databases = Database.objects.filter(instance_id=instance.instance_id)
    
    # 获取该实例下的所有账号
    accounts = DBAccount.objects.filter(instance_id=instance.instance_id)
    
    # 获取慢查询统计
    slow_query_stats = SlowQueryLog.objects.filter(
        instance_id=instance.instance_id
    ).values('database_name').annotate(
        count=Count('id'),
        avg_time=Avg('max_exe_time') / 1000.0,  # 转换为秒
        max_time=Max('max_exe_time') / 1000.0,  # 转换为秒
        min_time=Min('max_exe_time') / 1000.0,  # 转换为秒
        avg_rows=Avg('parse_row_count'),
        high_risk=Count('id', filter=Q(risk_level=2)),
        medium_risk=Count('id', filter=Q(risk_level=1)),
        low_risk=Count('id', filter=Q(risk_level=0))
    ).order_by('-count')
    
    # 获取近期的操作日志
    recent_logs = OperationLog.objects.filter(
        instance_string_id=instance.instance_id
    ).order_by('-operation_time')[:10]
    
    context = {
        'instance': instance,
        'databases': databases,
        'accounts': accounts,
        'slow_query_stats': slow_query_stats,
        'recent_logs': recent_logs
    }
    
    return render(request, 'rds_manager/instance_detail.html', context)

@login_required
@log_execution_time
def sync_instances(request):
    """同步RDS实例"""
    logger.debug(f"请求详情: {request}")
    logger.debug(f"请求方法: {request.method}")
    logger.debug(f"用户: {request.user.username} (ID: {request.user.id})")
    logger.debug(f"用户权限: is_superuser={request.user.is_superuser}, role={request.user.role}")
    
    if request.method == 'POST':
        try:
            result = InstanceSyncService.sync_all_instances(user=request.user)
            if 'error' in result:
                logger.error(f"同步实例失败: {result['error']}")
                return JsonResponse({'success': False, 'message': result['error']})
            
            message = f"同步完成。共发现{result['total']}个实例，新增{result['new']}个，更新{result['updated']}个，失败{result['failed']}个。"
            logger.info(message)
            return JsonResponse({'success': True, 'message': message})
        except Exception as e:
            logger.error(f"同步实例失败: {str(e)}")
            return JsonResponse({'success': False, 'message': f"同步失败: {str(e)}"})
    
    return JsonResponse({'success': False, 'message': '仅支持POST请求'})

@login_required
@log_execution_time
def sync_instance_databases(request, instance_id):
    """同步指定实例的数据库"""
    logger.debug(f"请求详情: {request}")
    logger.debug(f"请求方法: {request.method}")
    logger.debug(f"实例ID: {instance_id}")
    logger.debug(f"用户: {request.user.username} (ID: {request.user.id})")
    
    if request.method == 'POST':
        try:
            instance = get_object_or_404(RDSInstance, id=instance_id)
            
            # 记录用户权限信息
            logger.info(f"用户 {request.user.username} (ID: {request.user.id}) 尝试同步实例 {instance.instance_id}")
            logger.info(f"用户权限: is_superuser={request.user.is_superuser}, role={request.user.role}")
            
            # 检查权限
            has_permission = False
            if request.user.is_superuser or request.user.role == 'admin':
                has_permission = True
                logger.info("用户是超级管理员或管理员，允许访问")
            else:
                try:
                    permission = InstanceAccessPermission.objects.get(
                        user_id=request.user.id, 
                        instance_string_id=instance.instance_id
                    )
                    has_permission = True
                    logger.info(f"用户有实例访问权限: {permission.id}")
                except InstanceAccessPermission.DoesNotExist:
                    logger.warning(f"用户没有实例访问权限")
            
            if not has_permission:
                logger.warning(f"权限检查失败，拒绝访问")
                return JsonResponse({'success': False, 'message': '仅持有权限的用户可以访问'})
            
            # 使用实例的 instance_id 字符串而不是数据库 ID
            logger.debug(f"调用同步服务，实例ID: {instance.instance_id}")
            result = InstanceSyncService.sync_instance_databases(instance.instance_id, user=request.user)
            logger.debug(f"同步结果: {result}")
            
            if not result['success']:
                logger.error(f"同步失败: {result['error']}")
                return JsonResponse({'success': False, 'message': result['error']})
            
            messages.success(request, result['message'])
            logger.info(f"同步成功: {result['message']}")
            return JsonResponse({'success': True, 'message': result['message']})
        except Exception as e:
            logger.error(f"同步数据库失败: {str(e)}")
            return JsonResponse({'success': False, 'message': f"同步失败: {str(e)}"})
    
    return JsonResponse({'success': False, 'message': '仅支持POST请求'})

@login_required
def database_list(request, instance_id):
    """数据库列表视图"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取当前实例的数据库
    databases = Database.objects.filter(instance_id=instance.instance_id)
    
    # 为每个数据库添加慢查询统计
    for db in databases:
        db.slow_query_count = SlowQueryLog.objects.filter(
            instance_id=instance.instance_id, 
            database_name=db.name
        ).count()
    
    # 分页处理
    page = request.GET.get('page', 1)
    paginator = Paginator(databases, 10)  # 每页10条数据库记录
    
    try:
        databases_page = paginator.page(page)
    except:
        databases_page = paginator.page(1)
    
    # 按数据库名称分组统计慢查询数量
    slow_query_stats = SlowQueryLog.objects.filter(instance_id=instance.instance_id) \
                                        .values('database_name') \
                                        .annotate(count=Count('id')) \
                                        .order_by('-count')[:5]  # 获取前5个数据库
    
    context = {
        'instance': instance,
        'databases': databases_page,
        'slow_query_stats': slow_query_stats,
        'is_paginated': paginator.num_pages > 1,
        'page_obj': databases_page
    }
    
    return render(request, 'rds_manager/database_list.html', context)

@login_required
def database_detail(request, database_id):
    """数据库详情视图"""
    database = get_object_or_404(Database, id=database_id)
    
    # 查找对应的实例
    try:
        instance = RDSInstance.objects.get(instance_id=database.instance_id)
    except RDSInstance.DoesNotExist:
        instance = None
    
    # 获取这个数据库的账号权限
    privileges = DBAccountPrivilege.objects.filter(
        instance_id=database.instance_id,
        database_name=database.name
    )
    
    # 获取慢查询记录，并进行分页
    slow_queries = SlowQueryLog.objects.filter(
        instance_id=database.instance_id,
        database_name=database.name
    ).order_by('-timezone')
    
    # 分页处理
    page = request.GET.get('page', 1)
    paginator = Paginator(slow_queries, 10)  # 每页10条慢查询记录
    
    try:
        slow_queries_page = paginator.page(page)
    except:
        slow_queries_page = paginator.page(1)
    
    # 汇总统计
    total_slow_queries = slow_queries.count()
    avg_query_time = slow_queries.aggregate(avg_time=Avg('max_exe_time'))['avg_time'] or 0
    max_query_time = slow_queries.order_by('-max_exe_time').first().max_exe_time if total_slow_queries > 0 else 0
    avg_rows_examined = slow_queries.aggregate(avg_rows=Avg('parse_row_count'))['avg_rows'] or 0
    
    context = {
        'database': database,
        'instance': instance,
        'privileges': privileges,
        'slow_queries': slow_queries_page,
        'is_paginated': paginator.num_pages > 1,
        'page_obj': slow_queries_page,
        'total_slow_queries': total_slow_queries,
        'avg_query_time': avg_query_time / 1000.0,  # 转换为秒
        'max_query_time': max_query_time / 1000.0,  # 转换为秒
        'avg_rows_examined': avg_rows_examined
    }
    
    return render(request, 'rds_manager/database_detail.html', context)

@login_required
def account_list(request, instance_id):
    """账号列表视图"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取当前实例的账号
    accounts = DBAccount.objects.filter(instance_id=instance.instance_id)
    
    # 为每个账号添加权限信息
    for account in accounts:
        account.privileges = DBAccountPrivilege.objects.filter(
            instance_id=instance.instance_id,
            account_name=account.account_name
        )
    
    context = {
        'instance': instance,
        'accounts': accounts
    }
    
    return render(request, 'rds_manager/account_list.html', context)

@login_required
def log_list(request):
    """操作日志列表视图"""
    search = request.GET.get('search', '')
    instance_id = request.GET.get('instance_id', '')
    operation_type = request.GET.get('operation_type', '')
    date_range = request.GET.get('date_range', '')
    
    logs = OperationLog.objects.all().order_by('-operation_time')
    
    # 应用过滤条件
    if search:
        logs = logs.filter(
            operation_type__icontains=search) | logs.filter(
            operation_detail__icontains=search
        )
    
    if instance_id:
        logs = logs.filter(instance_string_id=instance_id)
    
    if operation_type:
        logs = logs.filter(operation_type=operation_type)
    
    # 日期范围过滤
    now = timezone.now()
    if date_range == 'today':
        logs = logs.filter(operation_time__date=now.date())
    elif date_range == 'yesterday':
        yesterday = now - timedelta(days=1)
        logs = logs.filter(operation_time__date=yesterday.date())
    elif date_range == 'last7days':
        seven_days_ago = now - timedelta(days=7)
        logs = logs.filter(operation_time__gte=seven_days_ago)
    elif date_range == 'last30days':
        thirty_days_ago = now - timedelta(days=30)
        logs = logs.filter(operation_time__gte=thirty_days_ago)
    
    # 分页
    paginator = Paginator(logs, 20)  # 每页显示20条
    page = request.GET.get('page')
    logs = paginator.get_page(page)
    
    # 获取操作类型选项
    operation_types = OperationLog.objects.values_list('operation_type', flat=True).distinct()
    
    context = {
        'logs': logs,
        'operation_types': operation_types
    }
    
    return render(request, 'rds_manager/log_list.html', context)

@login_required
def ajax_instance_databases(request, instance_id):
    """AJAX加载实例数据库列表"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取该实例的数据库
    databases = Database.objects.filter(instance_id=instance.instance_id)
    
    # 处理数据库分页
    page = request.GET.get('page', 1)
    paginator = Paginator(databases, 10)  # 每页10条记录
    
    try:
        databases_page = paginator.page(page)
    except:
        databases_page = paginator.page(1)
    
    context = {
        'instance': instance,
        'databases': databases_page,
        'total_databases': databases.count(),
    }
    
    return render(request, 'rds_manager/partials/instance_databases.html', context)

@login_required
def ajax_instance_accounts(request, instance_id):
    """AJAX加载实例账号列表"""
    instance = get_object_or_404(RDSInstance, id=instance_id)
    
    # 获取该实例的账号
    accounts = DBAccount.objects.filter(instance_id=instance.instance_id)
    
    # 处理账号分页
    page = request.GET.get('page', 1)
    paginator = Paginator(accounts, 10)  # 每页10条记录
    
    try:
        accounts_page = paginator.page(page)
    except:
        accounts_page = paginator.page(1)
    
    context = {
        'instance': instance,
        'accounts': accounts_page,
        'total_accounts': accounts.count(),
    }
    
    return render(request, 'rds_manager/partials/instance_accounts.html', context)

# API视图
class InstanceAPIView(APIView):
    """
    RDS实例API
    GET: 获取RDS实例列表
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        # 获取查询参数
        search_query = request.query_params.get('q', '')
        region_id = request.query_params.get('region', '')
        engine = request.query_params.get('engine', '')
        status = request.query_params.get('status', '')
        
        # 基本查询集
        instances = RDSInstance.objects.all()
        
        # 应用过滤条件
        if search_query:
            instances = instances.filter(
                Q(instance_id__icontains=search_query) |
                Q(instance_name__icontains=search_query) |
                Q(connection_string__icontains=search_query)
            )
        
        if region_id:
            region_obj = AliCloudRegion.objects.filter(region_id=region_id).first()
            if region_obj:
                instances = instances.filter(region_id=region_obj.id)
        
        if engine:
            instances = instances.filter(engine=engine)
        
        if status:
            instances = instances.filter(status=status)
        
        # 非管理员只能查看有权限的实例
        if not request.user.is_superuser and request.user.role != 'admin':
            user_permissions = InstanceAccessPermission.objects.filter(user_id=request.user.id)
            instance_ids = [perm.instance_string_id for perm in user_permissions]
            instances = instances.filter(instance_id__in=instance_ids)
        
        # 序列化数据
        data = []
        for instance in instances:
            region = instance.region
            data.append({
                'id': instance.id,
                'instance_id': instance.instance_id,
                'instance_name': instance.instance_name,
                'engine': instance.engine,
                'engine_version': instance.engine_version,
                'region': region.region_id if region else '',
                'status': instance.status,
                'status_display': dict(RDSInstance.STATUS_CHOICES).get(instance.status, instance.status),
                'connection_string': instance.connection_string,
                'port': instance.port,
                'create_time': instance.create_time.isoformat() if instance.create_time else None,
                'expire_time': instance.expire_time.isoformat() if instance.expire_time else None,
            })
        
        return Response(data, status=status.HTTP_200_OK)


class DatabaseAPIView(APIView):
    """
    数据库API
    GET: 获取指定实例的数据库列表
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, instance_id):
        instance = get_object_or_404(RDSInstance, instance_id=instance_id)
        
        # 记录用户权限信息
        logger.info(f"用户 {request.user.username} (ID: {request.user.id}) 尝试访问实例 {instance.instance_id}")
        logger.info(f"用户权限: is_superuser={request.user.is_superuser}, role={request.user.role}")
        
        # 检查权限
        has_permission = False
        if request.user.is_superuser or request.user.role == 'admin':
            has_permission = True
            logger.info("用户是超级管理员或管理员，允许访问")
        else:
            try:
                permission = InstanceAccessPermission.objects.get(
                    user_id=request.user.id, 
                    instance_string_id=instance.instance_id
                )
                has_permission = True
                logger.info(f"用户有实例访问权限: {permission.id}")
            except InstanceAccessPermission.DoesNotExist:
                logger.warning(f"用户没有实例访问权限")
        
        if not has_permission:
            return Response({'error': '无权访问该实例'}, status=status.HTTP_403_FORBIDDEN)
        
        databases = Database.objects.filter(instance_id=instance.instance_id)
        
        # 序列化数据
        data = []
        for db in databases:
            data.append({
                'id': db.id,
                'name': db.name,
                'character_set': db.character_set,
                'description': db.description,
                'created_at': db.created_at.isoformat(),
                'updated_at': db.updated_at.isoformat(),
            })
        
        return Response(data, status=status.HTTP_200_OK)


class AccountAPIView(APIView):
    """
    账号API
    GET: 获取指定实例的账号列表
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, instance_id):
        instance = get_object_or_404(RDSInstance, instance_id=instance_id)
        
        # 记录用户权限信息
        logger.info(f"用户 {request.user.username} (ID: {request.user.id}) 尝试访问实例 {instance.instance_id}")
        logger.info(f"用户权限: is_superuser={request.user.is_superuser}, role={request.user.role}")
        
        # 检查权限
        has_permission = False
        if request.user.is_superuser or request.user.role == 'admin':
            has_permission = True
            logger.info("用户是超级管理员或管理员，允许访问")
        else:
            try:
                permission = InstanceAccessPermission.objects.get(
                    user_id=request.user.id, 
                    instance_string_id=instance.instance_id
                )
                has_permission = True
                logger.info(f"用户有实例访问权限: {permission.id}")
            except InstanceAccessPermission.DoesNotExist:
                logger.warning(f"用户没有实例访问权限")
        
        if not has_permission:
            return Response({'error': '无权访问该实例'}, status=status.HTTP_403_FORBIDDEN)
        
        accounts = DBAccount.objects.filter(instance_id=instance.instance_id)
        
        # 序列化数据
        data = []
        for account in accounts:
            # 获取账号权限
            privileges = []
            for privilege in DBAccountPrivilege.objects.filter(
                instance_id=instance.instance_id,
                account_name=account.account_name
            ):
                privileges.append({
                    'database': privilege.database_name,
                    'privilege': privilege.privilege,
                    'privilege_display': dict(DBAccountPrivilege.PRIVILEGE_CHOICES).get(privilege.privilege, privilege.privilege),
                })
            
            data.append({
                'id': account.id,
                'account_name': account.account_name,
                'account_type': account.account_type,
                'account_status': account.account_status,
                'description': account.description,
                'created_at': account.created_at.isoformat(),
                'updated_at': account.updated_at.isoformat(),
                'privileges': privileges,
            })
        
        return Response(data, status=status.HTTP_200_OK)

@login_required
def slow_query_list(request):
    """慢查询列表视图"""
    # 获取查询参数
    instance_id = request.GET.get('instance_id', '')
    database = request.GET.get('database', '')
    query_date = request.GET.get('query_date', '')
    risk_score = request.GET.get('risk_score', '')
    execution_count = request.GET.get('execution_count', '')
    search = request.GET.get('search', '')
    
    # 基础查询
    base_query = SlowQueryLog.objects.all()
    
    # 过滤条件
    if instance_id:
        base_query = base_query.filter(instance_id=instance_id)
    
    if database:
        base_query = base_query.filter(database_name=database)
    
    # 查询日期
    if query_date:
        try:
            from datetime import datetime
            query_date_obj = datetime.strptime(query_date, '%Y-%m-%d').date()
            base_query = base_query.filter(timezone=query_date_obj)
        except ValueError:
            pass  # 忽略无效日期格式
    
    # 搜索
    if search:
        base_query = base_query.filter(
            Q(sql_text__icontains=search) |
            Q(database_name__icontains=search) |
            Q(instance_id__icontains=search)
        )
    
    # 按SQL hash聚合
    queries = base_query.values(
        'sql_hash',
        'instance_id',
        'database_name',
        'sql_text'
    ).annotate(
        id=Max('id'),  # 使用最新的记录ID
        total_count=Count('*'),  # 修改为 Count('*')
        total_sql_count=Sum('sql_count'),
        max_exe_time=Max('max_exe_time'),
        total_parse_rows=Sum('parse_row_count'),
        total_return_rows=Sum('return_row_count'),
        latest_date=Max('timezone'),
        risk_level=Max('risk_level')  # 使用最高风险级别
    ).order_by('-max_exe_time')  # 默认按最大执行时间降序
    
    # 风险评分过滤（需要关联SlowQueryAnalysis表）
    if risk_score:
        try:
            risk_score_value = int(risk_score)
            # 获取符合风险评分条件的sql_hash列表
            from .models import SlowQueryAnalysis
            qualified_hashes = SlowQueryAnalysis.objects.filter(
                risk_score__gte=risk_score_value
            ).values_list('sql_hash', flat=True).distinct()
            queries = queries.filter(sql_hash__in=qualified_hashes)
        except ValueError:
            pass  # 忽略无效的风险评分值
    
    # 执行次数过滤
    if execution_count:
        try:
            execution_count_value = int(execution_count)
            queries = queries.filter(total_sql_count__gte=execution_count_value)
        except ValueError:
            pass  # 忽略无效的执行次数值
    
    # 获取实例列表（用于过滤）- 使用select_related优化
    instances = RDSInstance.objects.only('id', 'instance_id', 'instance_name').all()
    
    # 获取数据库列表（用于过滤）- 使用缓存的方式
    databases = SlowQueryLog.objects.values_list('database_name', flat=True).distinct().order_by('database_name')
    
    # 分页处理（在转换为list之前进行分页，避免加载所有数据）
    page = request.GET.get('page', 1)
    paginator = Paginator(queries, 20)  # 每页20条记录
    
    try:
        slow_queries_page = paginator.page(page)
    except:
        slow_queries_page = paginator.page(1)
    
    # 只为当前页的查询结果添加关联的分析数据
    queries_list = list(slow_queries_page.object_list)
    
    # 批量获取分析数据，避免N+1查询问题
    if queries_list:
        from .models import SlowQueryAnalysis
        sql_hashes = [q['sql_hash'] for q in queries_list]
        instance_ids = [q['instance_id'] for q in queries_list]
        
        # 批量查询分析数据
        analysis_dict = {}
        analyses = SlowQueryAnalysis.objects.filter(
            sql_hash__in=sql_hashes,
            instance_id__in=instance_ids
        ).order_by('sql_hash', 'instance_id', '-timezone')
        
        # 构建分析数据字典，每个(sql_hash, instance_id)只保留最新的记录
        for analysis in analyses:
            key = (analysis.sql_hash, analysis.instance_id)
            if key not in analysis_dict:
                analysis_dict[key] = analysis
        
        # 为查询结果添加分析数据
        for query in queries_list:
            key = (query['sql_hash'], query['instance_id'])
            query['slowqueryanalysis'] = analysis_dict.get(key)
    
    # 创建分页对象
    slow_queries = slow_queries_page
    slow_queries.object_list = queries_list
    
    # 优化统计信息查询 - 使用单次查询获取所有统计
    total_slow_queries = paginator.count
    
    # 风险级别统计 - 只在需要时计算
    risk_stats = queries.aggregate(
        high_risk=Count('id', filter=Q(risk_level=2)),
        medium_risk=Count('id', filter=Q(risk_level=1)),
        low_risk=Count('id', filter=Q(risk_level=0))
    )
    high_risk_count = risk_stats['high_risk']
    medium_risk_count = risk_stats['medium_risk']
    low_risk_count = risk_stats['low_risk']
    
    # 按数据库分组统计 - 使用更高效的查询
    db_stats = base_query.values('database_name').annotate(
        count=Count('id')
    ).order_by('-count')[:5]
    
    context = {
        'slow_queries': slow_queries,
        'instances': instances,
        'databases': databases,
        'total_slow_queries': total_slow_queries,
        'high_risk_count': high_risk_count,
        'medium_risk_count': medium_risk_count,
        'low_risk_count': low_risk_count,
        'db_stats': db_stats,
        'filter_instance_id': instance_id,
        'filter_database': database,
        'filter_query_date': query_date,
        'filter_risk_score': risk_score,
        'filter_execution_count': execution_count,
        'filter_search': search
    }
    
    return render(request, 'rds_manager/slow_query_list.html', context)

@login_required
def slow_query_detail(request, query_id):
    """慢查询详情视图"""
    slow_query = get_object_or_404(SlowQueryLog, id=query_id)
    
    # 获取分析结果，如果不存在则创建
    try:
        analysis = SlowQueryAnalysis.objects.get(
            sql_hash=slow_query.sql_hash,
            instance_id=slow_query.instance_id,
            timezone=slow_query.timezone
        )
    except SlowQueryAnalysis.DoesNotExist:
        # 如果不存在分析记录，则进行分析
        analysis_result = SlowQueryService.analyze_slow_query(slow_query)
        analysis = analysis_result.get('analysis')
    
    # 获取实例信息
    try:
        instance = RDSInstance.objects.get(instance_id=slow_query.instance_id)
    except RDSInstance.DoesNotExist:
        instance = None
    
    # 查找相似的慢查询
    similar_queries = SlowQueryLog.objects.filter(
        database_name=slow_query.database_name,
        sql_hash=slow_query.sql_hash
    ).exclude(id=slow_query.id)[:5]
    
    # 获取趋势数据
    logger.info(f"获取慢查询趋势数据: sql_hash={slow_query.sql_hash}, instance_id={slow_query.instance_id}")
    
    # 直接获取所有匹配记录，避免聚合
    raw_data = SlowQueryLog.objects.filter(
        sql_hash=slow_query.sql_hash,
        instance_id=slow_query.instance_id
    ).order_by('timezone')
    
    # 记录找到的原始数据
    raw_count = raw_data.count()
    if raw_count > 0:
        logger.info(f"找到原始记录: {raw_count}条")
        for i, record in enumerate(raw_data):
            logger.info(f"记录{i+1}: id={record.id}, timezone={record.timezone}, max_exe_time={record.max_exe_time}")
    
    # 使用字典存储每天的聚合数据
    aggregated_data = {}
    for record in raw_data:
        date_str = record.timezone.strftime('%Y-%m-%d')
        if date_str not in aggregated_data:
            aggregated_data[date_str] = {
                'max_exe_time': record.max_exe_time,
                'sql_count': record.sql_count,
                'parse_row_count': record.parse_row_count,
                'return_row_count': record.return_row_count,
                'max_lock_time': record.max_lock_time  # 添加锁等待时间
            }
        else:
            # 更新最大值
            aggregated_data[date_str]['max_exe_time'] = max(aggregated_data[date_str]['max_exe_time'], record.max_exe_time)
            aggregated_data[date_str]['sql_count'] += record.sql_count
            aggregated_data[date_str]['parse_row_count'] += record.parse_row_count
            aggregated_data[date_str]['return_row_count'] += record.return_row_count
            aggregated_data[date_str]['max_lock_time'] = max(aggregated_data[date_str]['max_lock_time'], record.max_lock_time)  # 更新最大锁等待时间
    
    # 转换为有序列表
    trend_dates = []
    execution_times = []
    execution_counts = []
    scan_rows = []
    risk_scores = []
    return_rows = []  # 添加返回行数列表
    lock_times = []   # 添加锁等待时间列表
    
    # 获取排序后的日期
    sorted_dates = sorted(aggregated_data.keys())
    
    # 查询日期对应的风险评分记录
    analysis_records = {}
    date_str_list = sorted_dates  # 准备日期列表
    
    # 批量查询每个日期对应的分析记录
    if date_str_list:
        # 将字符串日期转换为日期对象进行查询
        date_objects = [datetime.strptime(date_str, '%Y-%m-%d').date() for date_str in date_str_list]
        analyses = SlowQueryAnalysis.objects.filter(
            sql_hash=slow_query.sql_hash,
            instance_id=slow_query.instance_id,
            timezone__in=date_objects
        )
        
        # 创建查找映射
        for analysis_record in analyses:
            date_key = analysis_record.timezone.strftime('%Y-%m-%d')
            analysis_records[date_key] = analysis_record
    
    # 为每个日期填充数据
    for date_str in sorted_dates:
        data = aggregated_data[date_str]
        trend_dates.append(date_str)
        execution_times.append(float(data['max_exe_time']))
        execution_counts.append(int(data['sql_count']))
        scan_rows.append(int(data['parse_row_count']))
        return_rows.append(int(data['return_row_count']))  # 添加返回行数数据
        lock_times.append(float(data['max_lock_time']))    # 添加锁等待时间数据
        
        # 如果存在预计算的风险评分，则使用它
        if date_str in analysis_records:
            risk_scores.append(analysis_records[date_str].risk_score)
        else:
            # 否则，计算该天的风险分数
            max_exe_time_score = min(data['max_exe_time'] / 1000, 100)
            scan_rows_score = min(data['parse_row_count'] / 10000, 100)
            sql_count_score = min(data['sql_count'] * 5, 100)
            
            risk_score = (max_exe_time_score * 0.6) + (scan_rows_score * 0.3) + (sql_count_score * 0.1)
            risk_scores.append(int(risk_score))
    
    # 记录最终数据
    logger.info(f"转换后的趋势数据: 日期数={len(trend_dates)}, 日期={trend_dates}")
    logger.info(f"执行时间数据: {execution_times}")
    logger.info(f"执行次数数据: {execution_counts}")
    logger.info(f"扫描行数数据: {scan_rows}")
    logger.info(f"返回行数数据: {return_rows}")  # 添加日志
    logger.info(f"锁等待时间数据: {lock_times}")  # 添加日志
    logger.info(f"风险分数数据: {risk_scores}")
    
    # 获取当前激活的慢查询评分配置
    try:
        from .models import SlowQueryScoreConfig
        active_config = SlowQueryScoreConfig.get_active_config(config_type='slowlog')
    except Exception as e:
        logger.error(f"获取激活配置失败: {e}")
        active_config = None

    # 构建评分详情
    score_details = {
        'total_score': analysis.risk_score,
        'query_count_score': analysis.query_count_score,
        'query_time_score': analysis.query_time_score,
        'scan_rows_score': analysis.scan_rows_score,
        'affected_rows_score': analysis.affected_rows_score,
        'lock_time_score': analysis.lock_time_score,
        # 添加权重信息
        'query_count_weight': active_config.query_count_weight if active_config else 0.4,
        'query_time_weight': active_config.query_time_weight if active_config else 0.3,
        'scan_rows_weight': active_config.scan_rows_weight if active_config else 0.2,
        'affected_rows_weight': active_config.affected_rows_weight if active_config else 0.05,
        'lock_time_weight': active_config.lock_time_weight if active_config else 0.05,
    }
    
    context = {
        'slow_query': slow_query,
        'analysis': analysis,
        'instance': instance,
        'similar_queries': similar_queries,
        'score_details': score_details,
        'trend_dates': json.dumps(trend_dates),
        'execution_times': json.dumps(execution_times),
        'execution_counts': json.dumps(execution_counts),
        'scan_rows': json.dumps(scan_rows),
        'risk_scores': json.dumps(risk_scores),
        'return_rows': json.dumps(return_rows),  # 添加到上下文
        'lock_times': json.dumps(lock_times)     # 添加到上下文
    }
    
    return render(request, 'rds_manager/slow_query_detail.html', context)

@login_required
def sync_slow_queries(request, instance_id):
    """同步慢查询统计信息"""
    if request.method != 'POST':
        return JsonResponse({'error': '仅支持POST请求'}, status=400)
    
    # 获取POST参数
    start_time_str = request.POST.get('start_time')
    end_time_str = request.POST.get('end_time')
    days_str = request.POST.get('days')
    
    # 优先使用days参数，其次使用start_time和end_time参数
    if days_str:
        try:
            days = int(days_str)
            end_time = timezone.now()
            start_time = end_time - timedelta(days=days)
            logger.info(f"使用days参数: {days}天, 日期范围: {start_time.date()} 到 {end_time.date()}")
        except ValueError:
            error_msg = 'days参数必须为整数'
            logger.error(error_msg)
            return JsonResponse({'success': False, 'error': error_msg}, status=400)
    # 如果没有提供days参数，尝试使用start_time和end_time参数
    elif start_time_str and end_time_str:
        # 解析日期
        try:
            start_time = datetime.strptime(start_time_str, '%Y-%m-%d')
            end_time = datetime.strptime(end_time_str, '%Y-%m-%d')
            # 将结束日期设置为当天的23:59:59
            end_time = end_time.replace(hour=23, minute=59, second=59)
        except ValueError:
            error_msg = '日期格式无效，请使用YYYY-MM-DD格式'
            logger.error(error_msg)
            return JsonResponse({'success': False, 'error': error_msg}, status=400)
    # 如果都没有提供，使用默认日期范围（最近3天）
    else:
        end_time = timezone.now()
        start_time = end_time - timedelta(days=3)  # 默认3天
        logger.info(f"未提供时间参数，使用默认时间范围：{start_time.date()} 到 {end_time.date()}")
    
    # 获取RDS实例
    try:
        instance = RDSInstance.objects.get(id=instance_id)
    except RDSInstance.DoesNotExist:
        error_msg = '实例不存在'
        logger.error(f"同步慢查询失败: {error_msg}")
        return JsonResponse({'success': False, 'error': error_msg}, status=404)
    
    # 记录同步开始信息
    logger.info(f"开始同步实例 {instance.instance_id} 的慢查询，时间范围: {start_time.date()} 到 {end_time.date()}")
    
    # 同步慢查询
    try:
        result = SlowQueryService.sync_slow_queries(
            instance_id=instance.instance_id,
            start_time=start_time,
            end_time=end_time,
            user=request.user
        )
        
        return JsonResponse(result)
    except Exception as e:
        error_msg = f'同步失败: {str(e)}'
        logger.error(f"同步慢查询失败: {error_msg}")
        return JsonResponse({'success': False, 'error': error_msg}, status=500)

@login_required
def get_sql_samples(request, query_id):
    """获取SQL样本数据的API端点"""
    slow_query = get_object_or_404(SlowQueryLog, id=query_id)
    
    # 获取实例信息
    try:
        instance = RDSInstance.objects.get(instance_id=slow_query.instance_id)
    except RDSInstance.DoesNotExist:
        instance = None
    
    # 获取SQL样本
    sql_samples = []
    try:
        if instance:
            # 创建RDS服务
            rds_service = AliCloudRDSService(region_id=instance.region.region_id if instance.region else None)
            
            # 记录调试信息
            logger.info(f"开始获取SQL样本: instance_id={instance.instance_id}, region_id={instance.region.region_id if instance.region else 'None'}")
            logger.info(f"SQL哈希: {slow_query.sql_hash}, 数据库: {slow_query.database_name}")
            
            # 获取慢查询的统计日期
            if hasattr(slow_query.timezone, 'date'):
                query_date = slow_query.timezone.date()  # 如果timezone是datetime对象
            else:
                query_date = slow_query.timezone  # 已经是date对象
            
            # 准备时间范围（只查询特定的一天）
            start_time = datetime.combine(query_date, datetime.min.time())
            start_time = timezone.make_aware(start_time)  # 转换为带时区的datetime
            
            end_time = datetime.combine(query_date, datetime.max.time())
            end_time = timezone.make_aware(end_time)  # 转换为带时区的datetime
            
            # 转换为阿里云API需要的时间格式 yyyy-MM-ddTHH:mmZ（注意这里的格式）
            start_time_str = start_time.astimezone(timezone.utc).strftime("%Y-%m-%dT%H:%MZ")
            end_time_str = end_time.astimezone(timezone.utc).strftime("%Y-%m-%dT%H:%MZ")
            
            logger.info(f"查询指定日期: {query_date}")
            logger.info(f"API查询时间范围: {start_time_str} 到 {end_time_str}")
            
            # 获取原始SQL文本用于模糊匹配
            sql_text = slow_query.sql_text or ""
            sql_text_normalized = ' '.join(sql_text.split()).lower()
            logger.info(f"原始SQL文本: {sql_text_normalized[:100]}...")
            
            # 获取SQL样本记录（使用慢查询统计里的数据库名和SQL哈希）
            records = rds_service.get_slow_query_records(
                instance_id=slow_query.instance_id,
                start_time=start_time_str,
                end_time=end_time_str,
                db_name=slow_query.database_name,
                sql_hash=slow_query.sql_hash,  # 直接在API中使用SQL哈希
            )
            
            logger.info(f"获取到的SQL样本数量: {len(records)}")
            
            # 最终返回的样本
            result_records = []
            
            # 统计匹配上的记录数
            match_count = 0
            
            # 只保留SQL匹配的样本（由于我们已通过API直接查询了指定SQL哈希，这里主要是二次确认）
            for record in records:
                if record.get('SQLHash') == slow_query.sql_hash:
                    result_records.append(record)
                    match_count += 1
            
            logger.info(f"匹配到的SQL样本数量: {match_count}")
            
            # 按照执行时间排序并且最多返回5条
            result_records = sorted(result_records, key=lambda x: x.get('QueryTimes', 0), reverse=True)[:5]
            
            logger.info(f"最终显示的SQL样本数量: {len(result_records)}")
            
            # 将筛选后的记录转换为样本数据
            for match in result_records:
                item = match
                execution_time = datetime.fromtimestamp(float(item.get('QueryTimes', 0)) / 1000, timezone.utc)
                execution_time_display = execution_time.strftime("%Y-%m-%d %H:%M:%S")
                
                sample = {
                    'sql_text': item.get('SQLText', ''),
                    'execution_time': execution_time_display,
                    'query_time': float(item.get('QueryTimes', 0)) * 1000,  # 转换为毫秒
                    'lock_time': float(item.get('LockTimes', 0)) * 1000,    # 转换为毫秒
                    'rows_examined': item.get('ParseRowCounts', 0),
                    'host_address': item.get('HostAddress', 'N/A'),  # 添加主机地址信息
                    'match_type': 'SQL匹配'
                }
                sql_samples.append(sample)
                logger.info(f"添加样本: 执行时间={execution_time_display}")
        else:
            logger.warning("API返回的响应中没有Items.SQLSlowRecord字段")
            if 'Code' in response:
                logger.warning(f"API返回错误代码: {response.get('Code')}, 消息: {response.get('Message', '')}")
    except Exception as e:
        logger.error(f"获取SQL样本出错: {str(e)}", exc_info=True)
    
    # 返回JSON响应
    return JsonResponse({
        'success': True,
        'samples': sql_samples
    })

@login_required
def score_config_list(request):
    """慢查询评分配置列表视图"""
    # 获取所有配置
    configs = SlowQueryScoreConfig.objects.all().order_by('-is_active', '-created_at')
    
    # 获取当前激活的配置（默认显示慢查询配置）
    try:
        active_config = SlowQueryScoreConfig.get_active_config(config_type='slowlog')
    except Exception:
        active_config = None
    
    context = {
        'configs': configs,
        'active_config': active_config
    }
    
    return render(request, 'rds_manager/score_config_list.html', context)

@login_required
def score_config_detail(request, config_id=None):
    """慢查询评分配置详情视图"""
    # 编辑现有配置或创建新配置
    if config_id:
        config = get_object_or_404(SlowQueryScoreConfig, id=config_id)
        is_new = False
    else:
        config = None
        is_new = True
    
    if request.method == 'POST':
        # 处理表单提交
        try:
            # 获取基本信息
            name = request.POST.get('name')
            description = request.POST.get('description', '')
            is_active = request.POST.get('is_active') == 'on'
            
            # 查询次数参数
            query_count_min = int(request.POST.get('query_count_min', 5))
            query_count_max = int(request.POST.get('query_count_max', 500))
            query_count_weight = float(request.POST.get('query_count_weight', 0.4))
            query_count_curve = request.POST.get('query_count_curve', 'linear')
            
            # 查询时间参数
            query_time_min = int(request.POST.get('query_time_min', 0))
            query_time_max = int(request.POST.get('query_time_max', 10000))
            query_time_weight = float(request.POST.get('query_time_weight', 0.3))
            query_time_curve = request.POST.get('query_time_curve', 'sine')
            
            # 扫描行数参数
            scan_rows_min = int(request.POST.get('scan_rows_min', 1))
            scan_rows_max = int(request.POST.get('scan_rows_max', 1000000))
            scan_rows_weight = float(request.POST.get('scan_rows_weight', 0.2))
            scan_rows_curve = request.POST.get('scan_rows_curve', 'polynomial')
            
            # 影响行数参数
            affected_rows_min = int(request.POST.get('affected_rows_min', 0))
            affected_rows_max = int(request.POST.get('affected_rows_max', 500))
            affected_rows_weight = float(request.POST.get('affected_rows_weight', 0.05))
            affected_rows_curve = request.POST.get('affected_rows_curve', 'linear')
            
            # 锁等待时间参数
            lock_time_min = int(request.POST.get('lock_time_min', 0))
            lock_time_max = int(request.POST.get('lock_time_max', 1000))
            lock_time_weight = float(request.POST.get('lock_time_weight', 0.05))
            lock_time_curve = request.POST.get('lock_time_curve', 'exponential')
            
            # 多项式系数
            polynomial_a = float(request.POST.get('polynomial_a', 0.0))
            polynomial_b = float(request.POST.get('polynomial_b', 0.02))
            polynomial_c = float(request.POST.get('polynomial_c', 0.001))
            polynomial_d = float(request.POST.get('polynomial_d', 0.0005))
            polynomial_e = float(request.POST.get('polynomial_e', 0.00001))
            polynomial_f = float(request.POST.get('polynomial_f', 0.000001))
            
            # 验证权重总和是否等于1
            weights_sum = query_count_weight + query_time_weight + scan_rows_weight + affected_rows_weight + lock_time_weight
            if not 0.99 <= weights_sum <= 1.01:
                messages.error(request, f"权重总和必须等于1，当前总和: {weights_sum:.2f}")
                raise ValueError("权重总和必须等于1")
            
            # 获取配置类型
            config_type = request.POST.get('config_type', 'slowlog')

            # 如果是激活配置，将同类型的其他配置设为非激活
            if is_active:
                SlowQueryScoreConfig.objects.filter(
                    config_type=config_type,
                    is_active=True
                ).update(is_active=False)
            
            # 创建或更新配置
            if is_new:
                config = SlowQueryScoreConfig.objects.create(
                    name=name,
                    config_type=config_type,
                    description=description,
                    is_active=is_active,
                    query_count_min=query_count_min,
                    query_count_max=query_count_max,
                    query_count_weight=query_count_weight,
                    query_count_curve=query_count_curve,
                    query_time_min=query_time_min,
                    query_time_max=query_time_max,
                    query_time_weight=query_time_weight,
                    query_time_curve=query_time_curve,
                    scan_rows_min=scan_rows_min,
                    scan_rows_max=scan_rows_max,
                    scan_rows_weight=scan_rows_weight,
                    scan_rows_curve=scan_rows_curve,
                    affected_rows_min=affected_rows_min,
                    affected_rows_max=affected_rows_max,
                    affected_rows_weight=affected_rows_weight,
                    affected_rows_curve=affected_rows_curve,
                    lock_time_min=lock_time_min,
                    lock_time_max=lock_time_max,
                    lock_time_weight=lock_time_weight,
                    lock_time_curve=lock_time_curve,
                    polynomial_a=polynomial_a,
                    polynomial_b=polynomial_b,
                    polynomial_c=polynomial_c,
                    polynomial_d=polynomial_d,
                    polynomial_e=polynomial_e,
                    polynomial_f=polynomial_f
                )
                messages.success(request, f"成功创建配置: {name}")
            else:
                # 更新现有配置
                config.name = name
                config.config_type = config_type
                config.description = description
                config.is_active = is_active
                config.query_count_min = query_count_min
                config.query_count_max = query_count_max
                config.query_count_weight = query_count_weight
                config.query_count_curve = query_count_curve
                config.query_time_min = query_time_min
                config.query_time_max = query_time_max
                config.query_time_weight = query_time_weight
                config.query_time_curve = query_time_curve
                config.scan_rows_min = scan_rows_min
                config.scan_rows_max = scan_rows_max
                config.scan_rows_weight = scan_rows_weight
                config.scan_rows_curve = scan_rows_curve
                config.affected_rows_min = affected_rows_min
                config.affected_rows_max = affected_rows_max
                config.affected_rows_weight = affected_rows_weight
                config.affected_rows_curve = affected_rows_curve
                config.lock_time_min = lock_time_min
                config.lock_time_max = lock_time_max
                config.lock_time_weight = lock_time_weight
                config.lock_time_curve = lock_time_curve
                config.polynomial_a = polynomial_a
                config.polynomial_b = polynomial_b
                config.polynomial_c = polynomial_c
                config.polynomial_d = polynomial_d
                config.polynomial_e = polynomial_e
                config.polynomial_f = polynomial_f
                config.save()
                messages.success(request, f"成功更新配置: {name}")
            
            # 重定向到列表页
            return redirect('rds_manager:score_config_list')
        
        except (ValueError, TypeError) as e:
            messages.error(request, f"表单数据错误: {str(e)}")
        except Exception as e:
            messages.error(request, f"保存配置失败: {str(e)}")
    
    # 如果是GET请求或处理失败的POST请求，显示表单
    context = {
        'config': config,
        'is_new': is_new,
        'curve_choices': SlowQueryScoreConfig.CURVE_CHOICES
    }
    
    return render(request, 'rds_manager/score_config_form.html', context)

@login_required
def score_config_activate(request, config_id):
    """激活指定的慢查询评分配置"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '仅支持POST请求'}, status=400)
    
    config = get_object_or_404(SlowQueryScoreConfig, id=config_id)
    
    try:
        # 将同类型的其他配置设为非激活
        SlowQueryScoreConfig.objects.filter(
            config_type=config.config_type,
            is_active=True
        ).update(is_active=False)

        # 激活当前配置
        config.is_active = True
        config.save()
        
        messages.success(request, f"已激活配置: {config.name}")
        return JsonResponse({'success': True})
    except Exception as e:
        messages.error(request, f"激活配置失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
def score_config_delete(request, config_id):
    """删除指定的慢查询评分配置"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '仅支持POST请求'}, status=400)
    
    config = get_object_or_404(SlowQueryScoreConfig, id=config_id)
    
    try:
        # 不允许删除激活的配置
        if config.is_active:
            messages.error(request, "不能删除激活的配置，请先激活其他配置")
            return JsonResponse({'success': False, 'error': '不能删除激活的配置'}, status=400)
        
        # 删除配置
        config_name = config.name
        config.delete()
        
        messages.success(request, f"已删除配置: {config_name}")
        return JsonResponse({'success': True})
    except Exception as e:
        messages.error(request, f"删除配置失败: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
def score_config_test(request, config_id=None):
    """测试慢查询评分配置"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': '仅支持POST请求'}, status=400)
    
    try:
        # 获取配置
        if config_id:
            config = get_object_or_404(SlowQueryScoreConfig, id=config_id)
        else:
            # 如果没有指定配置ID，使用表单中的数据创建临时配置
            config = SlowQueryScoreConfig(
                name="临时测试配置",
                config_type=request.POST.get('config_type', 'slowlog'),
                query_count_min=int(request.POST.get('query_count_min', 5)),
                query_count_max=int(request.POST.get('query_count_max', 500)),
                query_count_weight=float(request.POST.get('query_count_weight', 0.4)),
                query_count_curve=request.POST.get('query_count_curve', 'linear'),
                query_time_min=int(request.POST.get('query_time_min', 0)),
                query_time_max=int(request.POST.get('query_time_max', 10000)),
                query_time_weight=float(request.POST.get('query_time_weight', 0.3)),
                query_time_curve=request.POST.get('query_time_curve', 'sine'),
                scan_rows_min=int(request.POST.get('scan_rows_min', 1)),
                scan_rows_max=int(request.POST.get('scan_rows_max', 1000000)),
                scan_rows_weight=float(request.POST.get('scan_rows_weight', 0.2)),
                scan_rows_curve=request.POST.get('scan_rows_curve', 'polynomial'),
                affected_rows_min=int(request.POST.get('affected_rows_min', 0)),
                affected_rows_max=int(request.POST.get('affected_rows_max', 500)),
                affected_rows_weight=float(request.POST.get('affected_rows_weight', 0.05)),
                affected_rows_curve=request.POST.get('affected_rows_curve', 'linear'),
                lock_time_min=int(request.POST.get('lock_time_min', 0)),
                lock_time_max=int(request.POST.get('lock_time_max', 1000)),
                lock_time_weight=float(request.POST.get('lock_time_weight', 0.05)),
                lock_time_curve=request.POST.get('lock_time_curve', 'exponential'),
                polynomial_a=float(request.POST.get('polynomial_a', 0.0)),
                polynomial_b=float(request.POST.get('polynomial_b', 0.02)),
                polynomial_c=float(request.POST.get('polynomial_c', 0.001)),
                polynomial_d=float(request.POST.get('polynomial_d', 0.0005)),
                polynomial_e=float(request.POST.get('polynomial_e', 0.00001)),
                polynomial_f=float(request.POST.get('polynomial_f', 0.000001))
            )
        
        # 从请求中获取测试数据
        query_count = int(request.POST.get('test_query_count', 50))
        query_time = int(request.POST.get('test_query_time', 1000))
        scan_rows = int(request.POST.get('test_scan_rows', 10000))
        affected_rows = int(request.POST.get('test_affected_rows', 100))
        lock_time = int(request.POST.get('test_lock_time', 100))
        
        # 计算各项得分
        def calculate_score(value, min_val, max_val, curve_type, config):
            # 超出范围处理
            if value <= min_val:
                return 0
            if value >= max_val:
                return 1
                
            # 标准化到0-1区间
            normalized = (value - min_val) / (max_val - min_val)
            
            # 根据曲线类型计算
            if curve_type == 'linear':
                # 线性曲线: Y = X
                return normalized
            elif curve_type == 'exponential':
                # 指数曲线: Y = 2^(X * log₂(100)) / 100
                return pow(2, normalized * math.log2(100)) / 100
            elif curve_type == 'logarithmic':
                # 对数曲线: Y = log₁₀(9*X+1)
                return math.log10(9 * normalized + 1)
            elif curve_type == 'sine':
                # 正弦曲线: Y = sin(X * π/2)
                return math.sin(normalized * math.pi / 2)
            elif curve_type == 'polynomial':
                # 多项式曲线: Y = a + b·X + c·X² + d·X³ + e·X⁴ + f·X⁵
                a, b, c, d, e, f = config.polynomial_a, config.polynomial_b, config.polynomial_c, config.polynomial_d, config.polynomial_e, config.polynomial_f
                
                # 这里将范围缩小到0-20，适合多项式计算
                x = normalized * 20
                return min(1.0, a + b*x + c*x**2 + d*x**3 + e*x**4 + f*x**5)
            else:
                # 默认使用线性曲线
                return normalized
        
        # 计算各项得分
        query_count_score = calculate_score(query_count, config.query_count_min, config.query_count_max, config.query_count_curve, config)
        query_time_score = calculate_score(query_time, config.query_time_min, config.query_time_max, config.query_time_curve, config)
        scan_rows_score = calculate_score(scan_rows, config.scan_rows_min, config.scan_rows_max, config.scan_rows_curve, config)
        affected_rows_score = calculate_score(affected_rows, config.affected_rows_min, config.affected_rows_max, config.affected_rows_curve, config)
        lock_time_score = calculate_score(lock_time, config.lock_time_min, config.lock_time_max, config.lock_time_curve, config)
        
        # 计算总分
        total_score = (
            query_count_score * config.query_count_weight +
            query_time_score * config.query_time_weight +
            scan_rows_score * config.scan_rows_weight +
            affected_rows_score * config.affected_rows_weight +
            lock_time_score * config.lock_time_weight
        )
        
        # 总分(0-1)换算为风险级别(0-100)
        risk_score = int(total_score * 100)
        
        # 设置风险等级
        if risk_score >= 70:
            risk_level = "高风险"
        elif risk_score >= 30:
            risk_level = "中风险"
        else:
            risk_level = "低风险"
        
        # 返回测试结果
        result = {
            'success': True,
            'query_count_score': round(query_count_score * 100),
            'query_time_score': round(query_time_score * 100),
            'scan_rows_score': round(scan_rows_score * 100),
            'affected_rows_score': round(affected_rows_score * 100),
            'lock_time_score': round(lock_time_score * 100),
            'total_score': round(total_score * 100),
            'risk_level': risk_level
        }
        
        # 生成曲线数据点
        curves = {}
        for metric, curve_type in [
            ('query_count', config.query_count_curve),
            ('query_time', config.query_time_curve),
            ('scan_rows', config.scan_rows_curve),
            ('affected_rows', config.affected_rows_curve),
            ('lock_time', config.lock_time_curve)
        ]:
            # 获取相关参数
            if metric == 'query_count':
                min_val, max_val = config.query_count_min, config.query_count_max
                current_val = query_count
            elif metric == 'query_time':
                min_val, max_val = config.query_time_min, config.query_time_max
                current_val = query_time
            elif metric == 'scan_rows':
                min_val, max_val = config.scan_rows_min, config.scan_rows_max
                current_val = scan_rows
            elif metric == 'affected_rows':
                min_val, max_val = config.affected_rows_min, config.affected_rows_max
                current_val = affected_rows
            elif metric == 'lock_time':
                min_val, max_val = config.lock_time_min, config.lock_time_max
                current_val = lock_time
            
            # 生成20个数据点
            step = (max_val - min_val) / 19  # 19个间隔，共20个点
            points = []
            for i in range(20):
                x = min_val + i * step
                y = calculate_score(x, min_val, max_val, curve_type, config) * 100
                points.append({'x': x, 'y': y})
            
            # 添加实际值的点
            current_point = {
                'x': current_val,
                'y': calculate_score(current_val, min_val, max_val, curve_type, config) * 100,
                'is_current': True
            }
            
            curves[metric] = {
                'points': points,
                'current': current_point,
                'curve_type': curve_type
            }
        
        result['curves'] = curves
        
        return JsonResponse(result)
    
    except Exception as e:
        logger.error(f"测试评分配置失败: {str(e)}", exc_info=True)
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


# ==================== 应用管理相关视图 ====================

@login_required
@admin_required
def application_dashboard(request):
    """应用管理仪表盘"""
    # 统计数据
    from .models import BizDomain, ProductLine

    # 新版本统计
    biz_domain_count = BizDomain.objects.filter(is_active=True).count()
    product_line_count = ProductLine.objects.filter(is_active=True).count()
    app_count = Application.objects.filter(is_active=True).count()
    db_relation_count = Database.objects.filter(app_id__isnull=False).count()

    # 按业务域统计产品线数量
    biz_domains = BizDomain.objects.filter(is_active=True)
    domain_stats = []
    for domain in biz_domains:
        product_line_count_for_domain = ProductLine.objects.filter(
            biz_domain_id=domain.external_id,
            is_active=True
        ).count()
        domain.product_line_count = product_line_count_for_domain
        domain_stats.append(domain)

    # 按产品线数量排序并取前10个
    domain_stats = sorted(domain_stats, key=lambda x: x.product_line_count, reverse=True)[:10]

    # 兼容旧版本：保留原有业务域统计
    old_domain_count = BusinessDomain.objects.filter(is_active=True).count()

    # 按应用类型统计
    app_type_stats = Application.objects.filter(is_active=True).values('app_type').annotate(
        count=Count('id')
    ).order_by('-count')

    # 按环境统计
    env_stats = Application.objects.filter(is_active=True).values('environment').annotate(
        count=Count('id')
    ).order_by('-count')

    context = {
        'biz_domain_count': biz_domain_count,
        'product_line_count': product_line_count,
        'app_count': app_count,
        'db_relation_count': db_relation_count,
        'domain_stats': domain_stats,
        'app_type_stats': app_type_stats,
        'env_stats': env_stats,
        # 兼容性
        'domain_count': old_domain_count,
    }

    return render(request, 'rds_manager/application_dashboard.html', context)


@login_required
@admin_required
def business_domain_list(request):
    """业务域管理页面（新版本，支持两层结构）"""
    # 导入模型
    from .models import ProductLine, BizDomain

    # 获取搜索和筛选参数
    search_query = request.GET.get('search', '').strip()
    view_type = request.GET.get('view', 'biz_domain')  # biz_domain 或 product_line
    biz_domain_id = request.GET.get('biz_domain', '')

    context = {
        'search_query': search_query,
        'view_type': view_type,
        'current_biz_domain': biz_domain_id,
    }

    if view_type == 'product_line':
        # 产品线视图
        # 基础查询
        product_lines = ProductLine.objects.filter(is_active=True).order_by('biz_domain_id', 'name')

        # 搜索过滤
        if search_query:
            product_lines = product_lines.filter(
                Q(name__icontains=search_query) |
                Q(owner__icontains=search_query) |
                Q(sr_expert__icontains=search_query)
            )

        # 业务域过滤
        if biz_domain_id:
            product_lines = product_lines.filter(biz_domain_id=biz_domain_id)

        # 分页
        paginator = Paginator(product_lines, 20)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # 获取业务域列表用于筛选
        biz_domains = BizDomain.objects.filter(is_active=True).order_by('name')

        context.update({
            'page_obj': page_obj,
            'product_lines': page_obj,
            'biz_domains': biz_domains,
        })

        return render(request, 'rds_manager/product_line_list.html', context)

    else:
        # 业务域视图
        # 基础查询
        biz_domains = BizDomain.objects.filter(is_active=True).order_by('name')

        # 搜索过滤
        if search_query:
            biz_domains = biz_domains.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(owner__icontains=search_query) |
                Q(tech_owner__icontains=search_query)
            )

        # 为每个业务域添加产品线数量
        for domain in biz_domains:
            domain.product_line_count = ProductLine.objects.filter(
                biz_domain_id=domain.external_id,
                is_active=True
            ).count()

        # 分页
        paginator = Paginator(biz_domains, 20)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context.update({
            'page_obj': page_obj,
            'biz_domains': page_obj,
        })

        return render(request, 'rds_manager/biz_domain_list.html', context)


@login_required
@admin_required
def application_list(request):
    """应用列表"""
    applications = Application.objects.filter(is_active=True).order_by('business_domain_id', 'name')

    # 筛选
    domain_id = request.GET.get('domain')
    app_type = request.GET.get('type')
    environment = request.GET.get('env')

    if domain_id:
        applications = applications.filter(business_domain_id=domain_id)
    if app_type:
        applications = applications.filter(app_type=app_type)
    if environment:
        applications = applications.filter(environment=environment)

    # 为每个应用添加数据库数量
    for app in applications:
        app.db_count = Database.objects.filter(app_id=app.id).count()

    # 分页
    paginator = Paginator(applications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # 获取筛选选项
    domains = BusinessDomain.objects.filter(is_active=True).order_by('name')
    app_types = Application.APP_TYPE_CHOICES
    environments = Application.ENV_CHOICES

    context = {
        'page_obj': page_obj,
        'applications': page_obj,
        'domains': domains,
        'app_types': app_types,
        'environments': environments,
        'current_domain': domain_id,
        'current_type': app_type,
        'current_env': environment,
    }

    return render(request, 'rds_manager/application_list.html', context)


@login_required
@admin_required
def application_detail(request, app_id):
    """应用详情"""
    application = get_object_or_404(Application, id=app_id)

    # 如果是AJAX请求，返回JSON数据
    if (request.headers.get('X-Requested-With') == 'XMLHttpRequest' or
        request.content_type == 'application/json' or
        request.headers.get('Accept', '').find('application/json') != -1):

        # 调试信息
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"应用详情API - 应用ID: {application.id}, 产品线ID: {application.product_line_id}")

        # 获取产品线信息
        product_line_info = None
        product_line = None
        if application.product_line_id:
            try:
                from .models import ProductLine
                product_line = ProductLine.objects.get(id=application.product_line_id)
                product_line_info = {
                    'id': product_line.id,
                    'name': product_line.name
                }
                logger.info(f"找到产品线: ID={product_line.id}, 名称={product_line.name}")
            except ProductLine.DoesNotExist:
                logger.warning(f"产品线不存在: ID={application.product_line_id}")
        else:
            logger.info("应用未关联产品线")

        data = {
            'success': True,
            'application': {
                'id': application.id,
                'name': application.name,
                'description': application.description,
                'business_domain': {
                    'id': application.business_domain.id,
                    'name': application.business_domain.name
                } if application.business_domain else None,
                'product_line_id': application.product_line_id,
                'product_line': product_line_info,
                'tech_owner': {
                    'id': product_line.team_leader.id,
                    'username': product_line.team_leader.username
                } if product_line and product_line.team_leader else {
                    'username': product_line.team_leader_display
                } if product_line and product_line.team_leader_display else None,
                'business_owner': {
                    'id': application.business_owner.id,
                    'username': application.business_owner.username
                } if application.business_owner else None,
            }
        }
        return JsonResponse(data)

    # 获取关联的数据库（通过Database模型的app_id字段）
    app_databases = Database.objects.filter(app_id=app_id).order_by('instance_id', 'name')

    context = {
        'application': application,
        'app_databases': app_databases,
    }

    return render(request, 'rds_manager/application_detail.html', context)




@login_required
@admin_required
def database_assignment(request):
    """数据库分配管理界面"""
    # 获取搜索参数
    search_query = request.GET.get('search', '').strip()
    instance_filter = request.GET.get('instance', '')
    app_filter = request.GET.get('app', '')
    biz_domain_filter = request.GET.get('biz_domain', '')
    product_line_filter = request.GET.get('product_line', '')
    application_filter = request.GET.get('application', '')

    # 基础查询
    databases = Database.objects.all().order_by('instance_id', 'name')

    # 搜索过滤
    if search_query:
        # 获取匹配的实例ID列表
        matching_instance_ids = RDSInstance.objects.filter(
            instance_name__icontains=search_query
        ).values_list('instance_id', flat=True)

        databases = databases.filter(
            Q(name__icontains=search_query) |
            Q(instance_id__icontains=search_query) |
            Q(instance_id__in=matching_instance_ids)
        )

    # 实例过滤
    if instance_filter:
        databases = databases.filter(instance_id=instance_filter)

    # 应用过滤
    if app_filter:
        if app_filter == 'unassigned':
            databases = databases.filter(app_id__isnull=True)
        else:
            databases = databases.filter(app_id=app_filter)

    # 业务域过滤（通过产品线关联）
    if biz_domain_filter:
        # 先获取该业务域下的产品线ID列表
        from .models import ProductLine
        product_line_ids = ProductLine.objects.filter(
            biz_domain_id=biz_domain_filter,
            is_active=True
        ).values_list('external_id', flat=True)

        # 再获取这些产品线下的应用ID列表
        app_ids = Application.objects.filter(
            product_line_id__in=product_line_ids,
            is_active=True
        ).values_list('id', flat=True)
        databases = databases.filter(app_id__in=app_ids)

    # 产品线过滤
    if product_line_filter:
        # 获取该产品线下的应用ID列表
        app_ids = Application.objects.filter(
            product_line_id=product_line_filter,
            is_active=True
        ).values_list('id', flat=True)
        databases = databases.filter(app_id__in=app_ids)

    # 应用过滤
    if application_filter:
        if application_filter == 'unassigned':
            databases = databases.filter(app_id__isnull=True)
        else:
            databases = databases.filter(app_id=application_filter)

    # 分页
    paginator = Paginator(databases, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # 获取筛选选项
    instances = RDSInstance.objects.all().order_by('instance_name')

    # 获取所有应用（前端会根据需要进行筛选）
    # 应用绑定到产品线：显示所有应用
    # 数据库绑定到应用：只显示未分配数据库的应用
    applications = Application.objects.filter(
        is_active=True
    ).order_by('name')

    # 获取业务域和产品线数据（只显示有数据的）
    from .services import DashboardQueryService
    dashboard_service = DashboardQueryService()
    business_domains = dashboard_service.get_business_domains()
    product_lines = dashboard_service.get_product_lines()

    # 获取所有数据库（用于JavaScript计算）
    all_databases = Database.objects.all()

    context = {
        'page_obj': page_obj,
        'databases': page_obj,  # 分页后的数据库（用于显示）
        'all_databases': all_databases,  # 所有数据库（用于JavaScript计算）
        'instances': instances,
        'applications': applications,
        'business_domains': business_domains,
        'product_lines': product_lines,
        'search_query': search_query,
        'current_instance': instance_filter,
        'current_app': app_filter,
        'current_biz_domain': biz_domain_filter,
        'current_product_line': product_line_filter,
        'current_application': application_filter,
    }

    return render(request, 'rds_manager/database_assignment.html', context)


@login_required
@admin_required
@require_http_methods(["POST"])
def bind_product_line_to_app(request):
    """绑定产品线到应用"""
    try:
        app_id = request.POST.get('app_id')
        product_line_id = request.POST.get('product_line_id')

        if not app_id or not product_line_id:
            return JsonResponse({
                'success': False,
                'message': '应用ID和产品线ID不能为空'
            })

        # 获取应用
        try:
            app = Application.objects.get(id=app_id)
        except Application.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': '应用不存在'
            })

        # 验证产品线是否存在
        from .models import ProductLine
        try:
            product_line = ProductLine.objects.get(id=product_line_id)
        except ProductLine.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': '产品线不存在'
            })

        # 更新应用的产品线
        app.product_line_id = product_line.id
        app.save()

        return JsonResponse({
            'success': True,
            'message': f'成功将应用 {app.name} 绑定到产品线 {product_line.name}'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'绑定失败: {str(e)}'
        })


@login_required
@admin_required
def get_all_databases(request):
    """获取所有数据库列表"""
    try:
        databases = Database.objects.all().order_by('instance_id', 'name')

        database_list = []
        for db in databases:
            database_list.append({
                'id': db.id,
                'name': db.name,
                'instance_id': db.instance_id,
                'app_id': db.app_id,
                'description': db.description or ''
            })

        return JsonResponse({
            'success': True,
            'databases': database_list
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取数据库列表失败: {str(e)}'
        })





@login_required
@admin_required
@require_http_methods(["POST"])
def assign_database_to_app(request):
    """分配数据库给应用"""
    try:
        database_id = request.POST.get('database_id')
        app_id = request.POST.get('app_id')
        product_line_id = request.POST.get('product_line_id')
        tech_owner = request.POST.get('tech_owner')

        if not database_id:
            return JsonResponse({'success': False, 'error': '数据库ID不能为空'})

        database = get_object_or_404(Database, id=database_id)

        # 如果app_id为空，表示取消分配
        if not app_id or app_id == '':
            database.app_id = None
            database.assigned_by_id = None
            database.assigned_at = None
            message = f"已取消数据库 {database.name} 的应用分配"
        else:
            # 验证应用是否存在
            application = get_object_or_404(Application, id=app_id)

            # 更新应用的产品线信息
            if product_line_id:
                application.product_line_id = int(product_line_id)
                application.save()

                # 更新产品线的负责人信息
                if tech_owner:
                    try:
                        from .models import ProductLine
                        product_line = ProductLine.objects.get(id=int(product_line_id))
                        product_line.team_leader_name = tech_owner
                        product_line.save()
                    except ProductLine.DoesNotExist:
                        pass
            else:
                application.save()

            database.app_id = int(app_id)
            database.assigned_by_id = request.user.id
            database.assigned_at = timezone.now()
            message = f"已将数据库 {database.name} 分配给应用 {application.name}"

        database.save()

        # 返回更新后的数据
        response_data = {
            'success': True,
            'message': message,
            'database': {
                'id': database.id,
                'name': database.name,
                'instance_id': database.instance_id,
                'instance_name': database.instance.instance_name if database.instance else database.instance_id,
                'app_id': database.app_id,
                'app_name': database.application.name if database.application else None,
                'product_line_id': database.application.product_line_id if database.application else None,
                'product_line_name': database.application.product_line.name if database.application and database.application.product_line else None,
                'tech_owner': database.application.product_line.team_leader_display if database.application and database.application.product_line else None,
                'assigned_by': database.assigned_by.username if database.assigned_by else None,
                'assigned_at': database.assigned_at.strftime('%Y-%m-%d %H:%M:%S') if database.assigned_at else None,
            }
        }

        return JsonResponse(response_data)

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@require_http_methods(["POST"])
def generate_dashboard_snapshot(request):
    """
    生成数据库运行情况快照

    接收外部crontab调用，分析每日数据并生成快照记录
    支持幂等性操作，可重复调用

    POST /api/dashboard/generate-snapshot/
    Body: {"date": "2024-08-04"}  # 可选，默认为昨天
    """
    logger = logging.getLogger(__name__)

    try:
        # 解析请求参数
        data = {}
        if request.content_type == 'application/json':
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'message': '无效的JSON格式'
                }, status=400)

        snapshot_date = data.get('date')

        # 验证日期格式
        if snapshot_date:
            try:
                datetime.strptime(snapshot_date, '%Y-%m-%d')
            except ValueError:
                return JsonResponse({
                    'success': False,
                    'message': '日期格式错误，请使用YYYY-MM-DD格式'
                }, status=400)

        # 调用快照生成服务
        snapshot_service = DashboardSnapshotService()
        result = snapshot_service.generate_daily_snapshot(snapshot_date)

        # 记录日志
        if result['success']:
            logger.info(f"快照生成成功: {result['data']}")
        else:
            logger.error(f"快照生成失败: {result['message']}")

        # 返回结果
        status_code = 200 if result['success'] else 500
        return JsonResponse(result, status=status_code)

    except Exception as e:
        logger.error(f"快照生成接口异常: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'服务器内部错误: {str(e)}'
        }, status=500)


@login_required
def database_dashboard(request):
    """
    数据库运行情况看板主页面

    展示数据库表运行情况的综合看板，包括：
    1. 按业务域聚合数据，可选产品线过滤
    2. 展示周、月、季度维度的执行SQL数量
    3. 展示周、月、季度维度的慢查数量
    4. 展示周、月、季度维度的风险SQL数量
    5. 分析库表的数量，表行数的总量、平均行数、索引数量、大表数量
    """
    try:
        dashboard_service = DashboardQueryService()

        # 获取业务域列表
        business_domains = dashboard_service.get_business_domains()

        # 获取产品线列表
        product_lines = dashboard_service.get_product_lines()

        # 获取最近30天的汇总数据（用于初始展示）
        from datetime import date, timedelta
        end_date = date.today() - timedelta(days=1)  # 昨天
        start_date = end_date - timedelta(days=29)   # 最近30天

        # 获取总体汇总数据
        summary_result = dashboard_service.get_summary_data(
            start_date=start_date,
            end_date=end_date,
            period='day'
        )

        # 提取数据列表
        summary_data = summary_result.get('data', []) if isinstance(summary_result, dict) else []

        # 计算总计数据
        total_stats = {
            'total_sql_execution': sum(item.get('total_sql_execution', 0) for item in summary_data),
            'total_slow_queries': sum(item.get('total_slow_queries', 0) for item in summary_data),
            'total_risk_sql': sum(
                item.get('total_high_risk', 0) +
                item.get('total_medium_risk', 0) +
                item.get('total_low_risk', 0)
                for item in summary_data
            ),
            'total_tables': sum(item.get('total_tables', 0) for item in summary_data),
            'total_databases': sum(item.get('total_databases', 0) for item in summary_data),
            'total_large_tables': sum(item.get('total_large_tables', 0) for item in summary_data),
        }

        # 获取业务域排行榜
        domain_ranking = dashboard_service.get_domain_ranking(
            metric='sql_execution_count',
            limit=10,
            start_date=start_date,
            end_date=end_date
        )

        context = {
            'business_domains': business_domains,
            'product_lines': product_lines,
            'total_stats': total_stats,
            'domain_ranking': domain_ranking,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'page_title': '数据库运行情况看板',
        }

        return render(request, 'rds_manager/database_dashboard.html', context)

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"数据库看板页面加载失败: {str(e)}")
        messages.error(request, f'看板数据加载失败: {str(e)}')
        return render(request, 'rds_manager/database_dashboard.html', {
            'business_domains': [],
            'product_lines': [],
            'total_stats': {},
            'domain_ranking': [],
            'error_message': str(e)
        })


@csrf_exempt
@require_http_methods(["GET"])
def ajax_dashboard_summary(request):
    """
    AJAX接口：获取看板汇总数据

    参数:
        biz_domain_id: 业务域ID (可选)
        product_line_id: 产品线ID (可选)
        start_date: 开始日期 (可选)
        end_date: 结束日期 (可选)
        period: 聚合周期 (day/week/month/quarter, 默认day)
        page: 页码 (可选，默认1)
        page_size: 每页大小 (可选，默认20)
        order_by: 排序字段 (可选)
        order_direction: 排序方向 (asc/desc, 默认desc)
    """
    try:
        dashboard_service = DashboardQueryService()

        # 获取请求参数
        biz_domain_id = request.GET.get('biz_domain_id')
        product_line_id = request.GET.get('product_line_id')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        period = request.GET.get('period', 'day')

        # 分页参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        order_by = request.GET.get('order_by', 'period_date')
        order_direction = request.GET.get('order_direction', 'desc')

        # 参数验证
        if biz_domain_id and biz_domain_id.lower() in ['', 'null', 'undefined']:
            biz_domain_id = None
        if product_line_id and product_line_id.lower() in ['', 'null', 'undefined']:
            product_line_id = None

        # 日期格式验证
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({'success': False, 'message': '开始日期格式错误'}, status=400)

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({'success': False, 'message': '结束日期格式错误'}, status=400)

        # 参数验证
        if page <= 0:
            page = 1
        if page_size <= 0 or page_size > 100:
            page_size = 20
        if order_direction not in ['asc', 'desc']:
            order_direction = 'desc'

        # 获取汇总数据
        summary_data = dashboard_service.get_summary_data(
            biz_domain_id=biz_domain_id,
            product_line_id=product_line_id,
            start_date=start_date,
            end_date=end_date,
            period=period,
            page=page,
            page_size=page_size,
            order_by=order_by,
            order_direction=order_direction
        )

        return JsonResponse({
            'success': True,
            'data': summary_data.get('data', []),
            'count': summary_data.get('count', 0),
            'total_pages': summary_data.get('total_pages', 1),
            'current_page': page,
            'page_size': page_size
        })

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"获取看板汇总数据失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'获取数据失败: {str(e)}',
            'error_type': type(e).__name__
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def ajax_dashboard_trend(request):
    """
    AJAX接口：获取趋势数据

    参数:
        biz_domain_id: 业务域ID (可选)
        product_line_id: 产品线ID (可选)
        days: 天数 (默认30)
        metric: 指标名称 (默认sql_execution_count)
    """
    try:
        dashboard_service = DashboardQueryService()

        # 获取请求参数
        biz_domain_id = request.GET.get('biz_domain_id')
        product_line_id = request.GET.get('product_line_id')
        days = int(request.GET.get('days', 30))
        metric = request.GET.get('metric', 'sql_execution_count')

        # 参数验证
        if biz_domain_id and biz_domain_id.lower() in ['', 'null', 'undefined']:
            biz_domain_id = None
        if product_line_id and product_line_id.lower() in ['', 'null', 'undefined']:
            product_line_id = None

        if days <= 0 or days > 365:
            return JsonResponse({'success': False, 'message': '天数范围错误(1-365)'}, status=400)

        # 获取趋势数据
        trend_data = dashboard_service.get_trend_data(
            biz_domain_id=biz_domain_id,
            product_line_id=product_line_id,
            days=days,
            metric=metric
        )

        return JsonResponse({
            'success': True,
            'data': trend_data,
            'count': len(trend_data)
        })

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"获取趋势数据失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'获取数据失败: {str(e)}',
            'error_type': type(e).__name__
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def ajax_dashboard_ranking(request):
    """
    AJAX接口：获取业务域排行榜

    参数:
        metric: 排序指标 (默认sql_execution_count)
        limit: 返回数量 (默认10)
        start_date: 开始日期 (可选)
        end_date: 结束日期 (可选)
    """
    try:
        dashboard_service = DashboardQueryService()

        # 获取请求参数
        metric = request.GET.get('metric', 'sql_execution_count')
        limit = int(request.GET.get('limit', 10))
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')

        # 参数验证
        if limit <= 0 or limit > 50:
            return JsonResponse({'success': False, 'message': '返回数量范围错误(1-50)'}, status=400)

        # 日期格式验证
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({'success': False, 'message': '开始日期格式错误'}, status=400)

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({'success': False, 'message': '结束日期格式错误'}, status=400)

        # 获取排行榜数据
        ranking_data = dashboard_service.get_domain_ranking(
            metric=metric,
            limit=limit,
            start_date=start_date,
            end_date=end_date
        )

        return JsonResponse({
            'success': True,
            'data': ranking_data,
            'count': len(ranking_data)
        })

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"获取排行榜数据失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'获取数据失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def ajax_product_lines(request):
    """
    AJAX接口：获取产品线列表

    参数:
        biz_domain_id: 业务域ID (可选)
    """
    try:
        dashboard_service = DashboardQueryService()

        # 获取请求参数
        biz_domain_id = request.GET.get('biz_domain_id')

        # 参数验证
        if biz_domain_id and biz_domain_id.lower() in ['', 'null', 'undefined']:
            biz_domain_id = None

        # 获取产品线列表
        product_lines = dashboard_service.get_product_lines(biz_domain_id=biz_domain_id)

        return JsonResponse({
            'success': True,
            'data': product_lines,
            'count': len(product_lines)
        })

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"获取产品线列表失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'获取数据失败: {str(e)}'
        }, status=500)
