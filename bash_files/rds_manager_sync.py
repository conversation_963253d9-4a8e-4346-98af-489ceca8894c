#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RDS管理器数据同步脚本
负责从阿里云API获取所有RDS/PolarDB实例、数据库、账号信息并同步到本地数据库
"""

import pymysql
import json
import logging
import os
from datetime import datetime
from typing import List, Dict
from aliyunsdkcore.client import AcsClient
from aliyunsdkrds.request.v20140815 import DescribeDBInstancesRequest, DescribeDBInstanceAttributeRequest, DescribeDatabasesRequest, DescribeAccountsRequest
from aliyunsdkpolardb.request.v20170801 import DescribeDBClustersRequest, DescribeDBClusterEndpointsRequest, DescribeDBClusterAttributeRequest
import configparser

# 配置日志（日志文件放在脚本所在目录）
script_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(script_dir, 'rds_manager_sync.log')
error_log_file = os.path.join(script_dir, 'rds_manager_sync_error.log')

# 配置普通日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置错误日志
error_logger = logging.getLogger('error_logger')
error_logger.setLevel(logging.ERROR)
error_handler = logging.FileHandler(error_log_file)
error_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
error_logger.addHandler(error_handler)


class RDSManagerSync:
    def __init__(self, access_key_id: str, access_key_secret: str, region_id: str = 'cn-hangzhou',
                 result_db_config: Dict = None):
        """
        初始化RDS管理器同步器
        
        Args:
            access_key_id: 阿里云AccessKey ID
            access_key_secret: 阿里云AccessKey Secret
            region_id: 地域ID，默认杭州
            result_db_config: 结果数据库配置
        """
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.region_id = region_id
        self.client = AcsClient(access_key_id, access_key_secret, region_id)
        self.result_db_config = result_db_config
        
        # 统计信息
        self.stats = {
            'total_instances_processed': 0,
            'production_instances_found': 0,
            'non_production_instances_found': 0,
            'rds_instances_synced': 0,
            'rds_instances_created': 0,
            'rds_instances_updated': 0,
            'rds_databases_synced': 0,
            'rds_databases_created': 0,
            'rds_databases_updated': 0,
            'rds_accounts_synced': 0,
            'rds_accounts_created': 0,
            'rds_accounts_updated': 0
        }
    
    def get_rds_instances(self) -> List[Dict]:
        """获取RDS for MySQL实例列表"""
        logger.info("正在获取RDS for MySQL实例...")
        instances = []
        
        try:
            request = DescribeDBInstancesRequest.DescribeDBInstancesRequest()
            request.set_Engine('MySQL')
            request.set_PageSize(100)
            
            response = self.client.do_action_with_exception(request)
            result = json.loads(response)
            
            for item in result.get('Items', {}).get('DBInstance', []):
                instance_id = item.get('DBInstanceId', '')
                db_description = item.get('DBInstanceDescription', '')
                
                # 检查是否为生产环境实例（描述包含"pro"）
                is_production = 'pro' in db_description.lower()
                
                instances.append({
                    'instance_id': instance_id,
                    'type': 'RDS-MySQL',
                    'endpoint': item.get('ConnectionString'),
                    'port': item.get('Port', 3306),
                    'description': db_description,
                    'status': item.get('DBInstanceStatus'),
                    'engine': item.get('Engine', 'MySQL'),
                    'engine_version': item.get('EngineVersion'),
                    'instance_class': item.get('DBInstanceClass', ''),
                    'zone_id': item.get('ZoneId', ''),
                    'vpc_id': item.get('VpcId', ''),
                    'create_time': item.get('CreateTime', ''),
                    'expire_time': item.get('ExpireTime', ''),
                    'is_production': is_production  # 标记是否为生产环境
                })
                
                env_type = "生产" if is_production else "非生产"
                logger.info(f"找到RDS实例: {instance_id} - {db_description} ({env_type})")
                
            logger.info(f"共找到 {len(instances)} 个RDS MySQL实例")
            
        except Exception as e:
            error_msg = f"获取RDS实例失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            
        return instances
    
    def get_polardb_instances(self) -> List[Dict]:
        """获取PolarDB for MySQL实例列表"""
        logger.info("正在获取PolarDB for MySQL实例...")
        instances = []
        
        try:
            request = DescribeDBClustersRequest.DescribeDBClustersRequest()
            request.set_DBType('MySQL')
            request.set_PageSize(30)
            
            response = self.client.do_action_with_exception(request)
            result = json.loads(response)
            
            for item in result.get('Items', {}).get('DBCluster', []):
                cluster_id = item.get('DBClusterId', '')
                description = item.get('DBClusterDescription', '')
                
                # 检查是否为生产环境实例（描述包含"pro"）
                is_production = 'pro' in description.lower()
                
                instances.append({
                    'instance_id': cluster_id,
                    'type': 'PolarDB-MySQL',
                    'endpoint': None,  # 需要单独获取
                    'port': 3306,
                    'description': description,
                    'status': item.get('DBClusterStatus'),
                    'engine': 'MySQL',
                    'engine_version': item.get('DBVersion'),
                    'instance_class': item.get('DBNodeClass', ''),
                    'zone_id': item.get('ZoneIds', ''),
                    'vpc_id': item.get('VpcId', ''),
                    'create_time': item.get('CreateTime', ''),
                    'expire_time': item.get('ExpireTime', ''),
                    'is_production': is_production  # 标记是否为生产环境
                })
                
                env_type = "生产" if is_production else "非生产"
                logger.info(f"找到PolarDB实例: {cluster_id} - {description} ({env_type})")
                
            logger.info(f"共找到 {len(instances)} 个PolarDB MySQL实例")
            
        except Exception as e:
            error_msg = f"获取PolarDB实例失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            
        return instances
    
    def get_rds_instance_endpoint(self, instance_id: str) -> str:
        """通过API获取RDS实例连接地址"""
        try:
            request = DescribeDBInstancesRequest.DescribeDBInstancesRequest()
            request.set_DBInstanceId(instance_id)
            
            response = self.client.do_action_with_exception(request)
            result = json.loads(response)
            
            items = result.get('Items', {}).get('DBInstance', [])
            if items:
                endpoint = items[0].get('ConnectionString')
                port = items[0].get('Port', 3306)
                logger.info(f"RDS实例 {instance_id} 连接地址: {endpoint}:{port}")
                return endpoint
            else:
                logger.warning(f"未找到RDS实例 {instance_id}")
                return None
                
        except Exception as e:
            error_msg = f"获取RDS实例 {instance_id} 连接地址失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            return None
    
    def get_polardb_instance_endpoint(self, cluster_id: str) -> str:
        """获取PolarDB实例连接地址"""
        try:
            request = DescribeDBClusterEndpointsRequest.DescribeDBClusterEndpointsRequest()
            request.set_DBClusterId(cluster_id)
            
            response = self.client.do_action_with_exception(request)
            result = json.loads(response)
            
            items = result.get('Items', [])
            if items:
                # 寻找主连接地址 (EndpointType = 'Primary')
                primary_endpoint = None
                
                for endpoint in items:
                    if endpoint.get('EndpointType') == 'Primary':
                        address_items = endpoint.get('AddressItems', [])
                        if address_items:
                            address_item = address_items[0]
                            primary_endpoint = address_item.get('ConnectionString')
                            break
                
                # 如果没有找到主连接地址，使用第一个可用的
                if not primary_endpoint and items:
                    first_endpoint = items[0]
                    address_items = first_endpoint.get('AddressItems', [])
                    if address_items:
                        address_item = address_items[0]
                        primary_endpoint = address_item.get('ConnectionString')
                
                if primary_endpoint:
                    logger.info(f"PolarDB实例 {cluster_id} 连接地址: {primary_endpoint}:3306")
                    return primary_endpoint
                else:
                    logger.warning(f"PolarDB实例 {cluster_id} 未找到有效连接地址")
                    return None
            else:
                logger.warning(f"未找到PolarDB实例 {cluster_id} 的连接端点")
                return None
                
        except Exception as e:
            error_msg = f"获取PolarDB实例 {cluster_id} 连接地址失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            return None
    
    def get_instance_endpoint(self, instance: Dict) -> str:
        """根据实例信息获取连接地址"""
        instance_id = instance['instance_id']
        instance_type = instance['type']
        
        if instance_type == 'RDS-MySQL':
            # 对于RDS，如果已经有endpoint就直接使用，否则通过API获取
            if instance.get('endpoint'):
                return instance['endpoint']
            else:
                return self.get_rds_instance_endpoint(instance_id)
        elif instance_type == 'PolarDB-MySQL':
            # 对于PolarDB，总是通过API获取最新的连接地址
            return self.get_polardb_instance_endpoint(instance_id)
        else:
            logger.warning(f"未知实例类型: {instance_type}")
            return None
    
    def get_all_instances(self) -> List[Dict]:
        """获取所有数据库实例"""
        logger.info("开始获取所有数据库实例...")
        
        # 获取所有实例
        all_instances = []
        all_instances.extend(self.get_rds_instances())
        all_instances.extend(self.get_polardb_instances())
        
        # 为所有实例获取或验证连接地址
        for instance in all_instances:
            if not instance['endpoint']:
                endpoint = self.get_instance_endpoint(instance)
                instance['endpoint'] = endpoint
                if not endpoint:
                    logger.warning(f"实例 {instance['instance_id']} 无法获取连接地址")
        
        # 过滤掉无法获取连接地址的实例
        valid_instances = [inst for inst in all_instances if inst['endpoint']]
        
        # 统计实例数量
        production_count = len([inst for inst in valid_instances if inst.get('is_production', False)])
        non_production_count = len(valid_instances) - production_count
        
        self.stats['production_instances_found'] = production_count
        self.stats['non_production_instances_found'] = non_production_count
        
        logger.info(f"共找到 {len(valid_instances)} 个有效实例：{production_count} 个生产环境，{non_production_count} 个非生产环境")
        return valid_instances
    
    def connect_to_database(self, host: str, port: int, user: str, password: str, database: str = None):
        """连接到数据库"""
        try:
            connection = pymysql.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=database,
                charset='utf8mb4',
                autocommit=True,
                connect_timeout=15,
                read_timeout=60
            )
            return connection
        except Exception as e:
            error_msg = f"连接数据库失败 {host}:{port} - {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            return None
    
    def connect_to_result_database(self):
        """连接到结果数据库"""
        if not self.result_db_config:
            logger.error("结果数据库配置未提供")
            return None
        
        try:
            connection = pymysql.connect(
                host=self.result_db_config['host'],
                port=int(self.result_db_config['port']),
                user=self.result_db_config['username'],
                password=self.result_db_config['password'],
                database=self.result_db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info("成功连接到结果数据库")
            return connection
        except Exception as e:
            logger.error(f"连接结果数据库失败: {e}")
            return None
    
    def sync_rds_instance_to_db(self, result_connection, instance_info: Dict):
        """同步RDS实例信息到数据库"""
        try:
            current_time = datetime.now()
            
            with result_connection.cursor() as cursor:
                # 检查实例是否已存在
                check_sql = "SELECT id FROM rds_manager_rdsinstance WHERE instance_id = %s"
                cursor.execute(check_sql, (instance_info['instance_id'],))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新实例信息
                    update_sql = """
                    UPDATE rds_manager_rdsinstance SET
                        instance_name = %s, instance_type = %s, instance_class = %s,
                        engine = %s, engine_version = %s, status = %s, zone_id = %s,
                        connection_string = %s, port = %s, description = %s,
                        updated_at = %s, last_sync_time = %s
                    WHERE instance_id = %s
                    """
                    cursor.execute(update_sql, (
                        instance_info['description'], instance_info['type'], 
                        instance_info.get('instance_class', ''), instance_info.get('engine', 'MySQL'),
                        instance_info.get('engine_version', ''), instance_info.get('status', 'Running'),
                        instance_info.get('zone_id', ''), instance_info['endpoint'], instance_info['port'],
                        instance_info['description'], current_time, current_time,
                        instance_info['instance_id']
                    ))
                    self.stats['rds_instances_updated'] += 1
                    logger.debug(f"更新RDS实例: {instance_info['instance_id']}")
                else:
                    # 插入新实例
                    insert_sql = """
                    INSERT INTO rds_manager_rdsinstance (
                        instance_id, instance_name, instance_type, instance_class,
                        engine, engine_version, status, zone_id, connection_string, port,
                        description, region_id, vpc_id, created_at, updated_at, last_sync_time
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_sql, (
                        instance_info['instance_id'], instance_info['description'], instance_info['type'],
                        instance_info.get('instance_class', ''), instance_info.get('engine', 'MySQL'),
                        instance_info.get('engine_version', ''), instance_info.get('status', 'Running'),
                        instance_info.get('zone_id', ''), instance_info['endpoint'], instance_info['port'],
                        instance_info['description'], 1, instance_info.get('vpc_id', ''),
                        current_time, current_time, current_time
                    ))
                    self.stats['rds_instances_created'] += 1
                
                self.stats['rds_instances_synced'] += 1
                
        except Exception as e:
            error_msg = f"同步RDS实例信息失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            raise
    
    def get_rds_instance_databases(self, instance_id: str) -> List[Dict]:
        """获取RDS实例的数据库列表"""
        try:
            request = DescribeDatabasesRequest.DescribeDatabasesRequest()
            request.set_DBInstanceId(instance_id)
            
            response = self.client.do_action_with_exception(request)
            result = json.loads(response)
            
            databases = []
            for db in result.get('Databases', {}).get('Database', []):
                databases.append({
                    'name': db.get('DBName', ''),
                    'character_set': db.get('CharacterSetName', ''),
                    'description': db.get('DBDescription', ''),
                    'status': db.get('DBStatus', '')
                })
            
            logger.info(f"RDS实例 {instance_id} 共有 {len(databases)} 个数据库")
            return databases
            
        except Exception as e:
            logger.error(f"获取RDS实例 {instance_id} 数据库列表失败: {e}")
            return []
    
    def get_rds_instance_accounts(self, instance_id: str) -> List[Dict]:
        """获取RDS实例的账号列表"""
        try:
            request = DescribeAccountsRequest.DescribeAccountsRequest()
            request.set_DBInstanceId(instance_id)
            
            response = self.client.do_action_with_exception(request)
            result = json.loads(response)
            
            accounts = []
            for account in result.get('Accounts', {}).get('DBInstanceAccount', []):
                accounts.append({
                    'name': account.get('AccountName', ''),
                    'type': account.get('AccountType', ''),
                    'status': account.get('AccountStatus', ''),
                    'description': account.get('AccountDescription', '')
                })
            
            logger.info(f"RDS实例 {instance_id} 共有 {len(accounts)} 个账号")
            return accounts
            
        except Exception as e:
            logger.error(f"获取RDS实例 {instance_id} 账号列表失败: {e}")
            return []
    
    def get_polardb_instance_databases(self, cluster_id: str, connection=None) -> List[Dict]:
        """获取PolarDB实例的数据库列表（通过直连数据库获取）"""
        try:
            # 注意：PolarDB的数据库和账号信息通常需要通过直连数据库来获取
            # 因为PolarDB API可能不提供这些详细信息的查询接口
            if not connection:
                logger.warning(f"PolarDB实例 {cluster_id} 需要数据库连接来获取数据库列表")
                return []
            
            databases = []
            with connection.cursor() as cursor:
                # 查询所有数据库
                cursor.execute("SHOW DATABASES")
                db_names = [row[0] for row in cursor.fetchall()]
                
                for db_name in db_names:
                    # 跳过系统数据库
                    if db_name in ['information_schema', 'performance_schema', 'mysql', 'sys']:
                        continue
                    
                    # 获取数据库字符集
                    cursor.execute(f"SELECT DEFAULT_CHARACTER_SET_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME = %s", (db_name,))
                    charset_result = cursor.fetchone()
                    charset = charset_result[0] if charset_result else 'utf8mb4'
                    
                    databases.append({
                        'name': db_name,
                        'character_set': charset,
                        'description': f'PolarDB数据库 {db_name}',
                        'status': 'Running'
                    })
            
            logger.info(f"PolarDB实例 {cluster_id} 共有 {len(databases)} 个数据库")
            return databases
            
        except Exception as e:
            logger.error(f"获取PolarDB实例 {cluster_id} 数据库列表失败: {e}")
            return []
    
    def get_polardb_instance_accounts(self, cluster_id: str, connection=None) -> List[Dict]:
        """获取PolarDB实例的账号列表（通过直连数据库获取）"""
        try:
            # 注意：PolarDB的账号信息通常需要通过直连数据库来获取
            if not connection:
                logger.warning(f"PolarDB实例 {cluster_id} 需要数据库连接来获取账号列表")
                return []
            
            accounts = []
            with connection.cursor() as cursor:
                # 查询所有用户账号
                cursor.execute("SELECT User, Host FROM mysql.user WHERE User != '' ORDER BY User")
                users = cursor.fetchall()
                
                for user, host in users:
                    # 跳过系统账号
                    if user in ['root', 'mysql.session', 'mysql.sys', 'mysql.infoschema']:
                        continue
                    
                    accounts.append({
                        'name': user,
                        'type': 'Normal',  # PolarDB通常不区分Super/Normal类型
                        'status': 'Available',
                        'description': f'PolarDB账号 {user}@{host}'
                    })
            
            logger.info(f"PolarDB实例 {cluster_id} 共有 {len(accounts)} 个账号")
            return accounts
            
        except Exception as e:
            logger.error(f"获取PolarDB实例 {cluster_id} 账号列表失败: {e}")
            return []
    
    def sync_rds_databases_to_db(self, result_connection, instance_id: str, databases: List[Dict]):
        """同步RDS实例数据库信息到数据库"""
        try:
            current_time = datetime.now()
            
            with result_connection.cursor() as cursor:
                for db_info in databases:
                    # 检查数据库是否已存在
                    check_sql = """
                    SELECT id FROM rds_manager_database 
                    WHERE instance_id = %s AND name = %s
                    """
                    cursor.execute(check_sql, (instance_id, db_info['name']))
                    existing = cursor.fetchone()
                    
                    if existing:
                        # 更新数据库信息
                        update_sql = """
                        UPDATE rds_manager_database SET
                            character_set = %s, description = %s, updated_at = %s
                        WHERE instance_id = %s AND name = %s
                        """
                        cursor.execute(update_sql, (
                            db_info['character_set'], db_info['description'], current_time,
                            instance_id, db_info['name']
                        ))
                        self.stats['rds_databases_updated'] += 1
                    else:
                        # 插入新数据库
                        insert_sql = """
                        INSERT INTO rds_manager_database (
                            instance_id, name, character_set, description, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(insert_sql, (
                            instance_id, db_info['name'], db_info['character_set'],
                            db_info['description'], current_time, current_time
                        ))
                        self.stats['rds_databases_created'] += 1
                    
                    self.stats['rds_databases_synced'] += 1
                    
        except Exception as e:
            error_msg = f"同步RDS数据库信息失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            raise
    
    def sync_rds_accounts_to_db(self, result_connection, instance_id: str, accounts: List[Dict]):
        """同步RDS实例账号信息到数据库"""
        try:
            current_time = datetime.now()
            
            with result_connection.cursor() as cursor:
                for account_info in accounts:
                    # 检查账号是否已存在
                    check_sql = """
                    SELECT id FROM rds_manager_dbaccount 
                    WHERE instance_id = %s AND account_name = %s
                    """
                    cursor.execute(check_sql, (instance_id, account_info['name']))
                    existing = cursor.fetchone()
                    
                    if existing:
                        # 更新账号信息
                        update_sql = """
                        UPDATE rds_manager_dbaccount SET
                            account_type = %s, account_status = %s, description = %s, updated_at = %s
                        WHERE instance_id = %s AND account_name = %s
                        """
                        cursor.execute(update_sql, (
                            account_info['type'], account_info['status'], account_info['description'],
                            current_time, instance_id, account_info['name']
                        ))
                        self.stats['rds_accounts_updated'] += 1
                    else:
                        # 插入新账号
                        insert_sql = """
                        INSERT INTO rds_manager_dbaccount (
                            instance_id, account_name, account_type, account_status, 
                            description, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(insert_sql, (
                            instance_id, account_info['name'], account_info['type'],
                            account_info['status'], account_info['description'],
                            current_time, current_time
                        ))
                        self.stats['rds_accounts_created'] += 1
                    
                    self.stats['rds_accounts_synced'] += 1
                    
        except Exception as e:
            error_msg = f"同步RDS账号信息失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            raise
    
    def sync_instance_data(self, instance: Dict, db_user: str, db_password: str):
        """同步单个实例的数据到数据库"""
        instance_id = instance['instance_id']
        instance_type = instance['type']
        endpoint = instance['endpoint']
        port = instance['port']
        is_production = instance.get('is_production', False)
        env_type = "生产" if is_production else "非生产"
        
        logger.info(f"开始同步实例数据: {instance_id} ({instance_type}, {env_type}环境)")
        
        # 连接结果数据库
        result_connection = self.connect_to_result_database()
        if not result_connection:
            logger.error("无法连接到结果数据库，跳过此实例")
            return
        
        try:
            # 1. 同步实例信息
            self.sync_rds_instance_to_db(result_connection, instance)
            
            # 2. 同步数据库信息
            if instance_type == 'RDS-MySQL':
                databases = self.get_rds_instance_databases(instance_id)
                if databases:
                    self.sync_rds_databases_to_db(result_connection, instance_id, databases)
                
                # 3. 同步账号信息
                accounts = self.get_rds_instance_accounts(instance_id)
                if accounts:
                    self.sync_rds_accounts_to_db(result_connection, instance_id, accounts)
            
            elif instance_type == 'PolarDB-MySQL':
                # 需要连接到源数据库获取PolarDB的数据库和账号信息
                source_connection = self.connect_to_database(endpoint, port, db_user, db_password)
                if source_connection:
                    try:
                        # 2. 同步PolarDB数据库信息
                        databases = self.get_polardb_instance_databases(instance_id, source_connection)
                        if databases:
                            self.sync_rds_databases_to_db(result_connection, instance_id, databases)
                        
                        # 3. 同步PolarDB账号信息
                        accounts = self.get_polardb_instance_accounts(instance_id, source_connection)
                        if accounts:
                            self.sync_rds_accounts_to_db(result_connection, instance_id, accounts)
                    finally:
                        source_connection.close()
                else:
                    logger.warning(f"无法连接到PolarDB实例 {instance_id}，跳过数据库和账号同步")
            
            # 提交事务
            result_connection.commit()
            self.stats['total_instances_processed'] += 1
            logger.info(f"实例数据同步完成: {instance_id}")
            
        except Exception as e:
            result_connection.rollback()
            error_msg = f"同步实例数据失败 {instance_id}: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
        finally:
            result_connection.close()
    
    def run_sync(self, rds_user: str, rds_password: str, polardb_user: str, polardb_password: str):
        """运行同步任务"""
        logger.info("开始RDS管理器数据同步")
        logger.info("策略: 同步所有实例的管理数据到本地数据库")
        
        if not self.result_db_config:
            logger.error("未配置结果数据库，无法运行同步任务")
            return
        
        # 获取所有实例
        instances = self.get_all_instances()
        
        if not instances:
            logger.warning("未找到任何实例")
            return
        
        # 同步每个实例的数据
        for instance in instances:
            try:
                if instance['type'] == 'RDS-MySQL':
                    self.sync_instance_data(instance, rds_user, rds_password)
                elif instance['type'] == 'PolarDB-MySQL':
                    self.sync_instance_data(instance, polardb_user, polardb_password)
            except Exception as e:
                error_msg = f"处理实例 {instance['instance_id']} 失败: {e}"
                logger.error(error_msg)
                error_logger.error(error_msg)
                continue
        
        # 输出统计信息
        self.output_stats()
    
    def output_stats(self):
        """输出统计信息"""
        logger.info(f"同步任务完成统计:")
        logger.info(f"- 实例发现统计:")
        logger.info(f"  * 生产环境实例: {self.stats['production_instances_found']}")
        logger.info(f"  * 非生产环境实例: {self.stats['non_production_instances_found']}")
        logger.info(f"  * 成功处理实例: {self.stats['total_instances_processed']}")
        logger.info(f"- RDS管理器同步统计:")
        logger.info(f"  * RDS实例 - 同步: {self.stats['rds_instances_synced']}, 新增: {self.stats['rds_instances_created']}, 更新: {self.stats['rds_instances_updated']}")
        logger.info(f"  * RDS数据库 - 同步: {self.stats['rds_databases_synced']}, 新增: {self.stats['rds_databases_created']}, 更新: {self.stats['rds_databases_updated']}")
        logger.info(f"  * RDS账号 - 同步: {self.stats['rds_accounts_synced']}, 新增: {self.stats['rds_accounts_created']}, 更新: {self.stats['rds_accounts_updated']}")
        
        # 输出统计信息到错误日志
        stats_summary = (
            f"RDS管理器同步任务完成: "
            f"生产环境实例={self.stats['production_instances_found']}, "
            f"非生产环境实例={self.stats['non_production_instances_found']}, "
            f"成功处理实例={self.stats['total_instances_processed']}, "
            f"RDS实例同步={self.stats['rds_instances_synced']}, "
            f"RDS实例新增={self.stats['rds_instances_created']}, "
            f"RDS实例更新={self.stats['rds_instances_updated']}, "
            f"RDS数据库同步={self.stats['rds_databases_synced']}, "
            f"RDS数据库新增={self.stats['rds_databases_created']}, "
            f"RDS数据库更新={self.stats['rds_databases_updated']}, "
            f"RDS账号同步={self.stats['rds_accounts_synced']}, "
            f"RDS账号新增={self.stats['rds_accounts_created']}, "
            f"RDS账号更新={self.stats['rds_accounts_updated']}"
        )
        error_logger.error(stats_summary)


def load_config(config_file: str = None) -> Dict:
    """加载配置文件"""
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 如果没有指定配置文件，使用脚本目录下的config.ini
    if config_file is None:
        config_file = os.path.join(script_dir, 'config.ini')
    elif not os.path.isabs(config_file):
        # 如果是相对路径，相对于脚本目录
        config_file = os.path.join(script_dir, config_file)
    
    config = configparser.ConfigParser()
    try:
        logger.info(f"尝试加载配置文件: {config_file}")
        if not os.path.exists(config_file):
            logger.warning(f"配置文件不存在: {config_file}")
            return {}
        
        config.read(config_file, encoding='utf-8')
        logger.info(f"成功加载配置文件: {config_file}")
        
        # 基础配置
        result = {
            'access_key_id': config.get('aliyun', 'access_key_id'),
            'access_key_secret': config.get('aliyun', 'access_key_secret'),
            'region_id': config.get('aliyun', 'region_id', fallback='cn-hangzhou'),
            'rds_user': config.get('database', 'rds_username'),
            'rds_password': config.get('database', 'rds_password'),
            'polardb_user': config.get('database', 'polardb_username'),
            'polardb_password': config.get('database', 'polardb_password')
        }
        
        # 结果数据库配置（必需）
        if config.has_section('result_database'):
            result['result_db_config'] = {
                'host': config.get('result_database', 'host'),
                'port': config.getint('result_database', 'port', fallback=3306),
                'username': config.get('result_database', 'username'),
                'password': config.get('result_database', 'password'),
                'database': config.get('result_database', 'database')
            }
        else:
            logger.error("配置文件缺少 [result_database] 配置段")
            result['result_db_config'] = None
            
        return result
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return {}


def main():
    """主函数"""
    print("RDS管理器数据同步脚本")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    
    if not config:
        print("配置文件加载失败，请手动输入配置")
        access_key_id = input("请输入阿里云 Access Key ID: ").strip()
        access_key_secret = input("请输入阿里云 Access Key Secret: ").strip()
        region_id = input("请输入地域ID (默认: cn-hangzhou): ").strip() or 'cn-hangzhou'
        rds_user = input("请输入RDS数据库用户名: ").strip()
        rds_password = input("请输入RDS数据库密码: ").strip()
        polardb_user = input("请输入PolarDB数据库用户名: ").strip()
        polardb_password = input("请输入PolarDB数据库密码: ").strip()
        
        # 结果数据库配置
        print("\n请输入结果数据库配置:")
        result_db_config = {
            'host': input("数据库主机: ").strip(),
            'port': int(input("数据库端口 (默认: 3306): ").strip() or "3306"),
            'username': input("数据库用户名: ").strip(),
            'password': input("数据库密码: ").strip(),
            'database': input("数据库名: ").strip()
        }
    else:
        access_key_id = config['access_key_id']
        access_key_secret = config['access_key_secret']
        region_id = config['region_id']
        rds_user = config['rds_user']
        rds_password = config['rds_password']
        polardb_user = config['polardb_user']
        polardb_password = config['polardb_password']
        result_db_config = config.get('result_db_config')
    
    if not result_db_config:
        print("错误: 结果数据库配置不完整，无法运行同步任务")
        return
    
    # 创建同步器
    sync = RDSManagerSync(access_key_id, access_key_secret, region_id, result_db_config)
    
    try:
        # 运行同步
        sync.run_sync(rds_user, rds_password, polardb_user, polardb_password)
        
        print(f"\n同步任务完成!")
        print(f"- 实例发现统计:")
        print(f"  * 生产环境实例: {sync.stats['production_instances_found']}")
        print(f"  * 非生产环境实例: {sync.stats['non_production_instances_found']}")
        print(f"  * 成功处理实例: {sync.stats['total_instances_processed']}")
        print(f"- RDS管理器同步统计:")
        print(f"  * 实例: 同步 {sync.stats['rds_instances_synced']}, 新增 {sync.stats['rds_instances_created']}, 更新 {sync.stats['rds_instances_updated']}")
        print(f"  * 数据库: 同步 {sync.stats['rds_databases_synced']}, 新增 {sync.stats['rds_databases_created']}, 更新 {sync.stats['rds_databases_updated']}")
        print(f"  * 账号: 同步 {sync.stats['rds_accounts_synced']}, 新增 {sync.stats['rds_accounts_created']}, 更新 {sync.stats['rds_accounts_updated']}")
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"同步过程中出现错误: {e}")


if __name__ == "__main__":
    main() 