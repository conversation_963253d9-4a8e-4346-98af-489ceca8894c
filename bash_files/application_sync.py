#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用系统同步脚本
从Poseidon API获取应用信息并同步到本地数据库
"""

import requests
import pymysql
import json
import logging
import os
import configparser
from datetime import datetime
from typing import List, Dict, Optional

# 配置日志
script_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(script_dir, 'application_sync.log')
error_log_file = os.path.join(script_dir, 'application_sync_error.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 错误日志
error_logger = logging.getLogger('error_logger')
error_logger.setLevel(logging.ERROR)
error_handler = logging.FileHandler(error_log_file)
error_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
error_logger.addHandler(error_handler)


class ApplicationSync:
    def __init__(self, api_base_url: str, db_config: Dict, bd_db_config: Dict = None):
        """
        初始化应用同步器

        Args:
            api_base_url: Poseidon API基础URL
            db_config: 数据库配置
            bd_db_config: 业务域数据源配置
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.db_config = db_config
        self.bd_db_config = bd_db_config
        
        # 应用类型映射
        self.app_type_mapping = {
            'JAR_APP': 'microservice',
            'WAR_APP': 'web',
            'DOCKER_APP': 'microservice',
            'NODE_APP': 'web',
            'PYTHON_APP': 'api',
            'GO_APP': 'api',
            'PHP_APP': 'web',
        }
        
        # 统计信息
        self.stats = {
            'total_apps_fetched': 0,
            'apps_created': 0,
            'apps_updated': 0,
            'apps_skipped': 0,
            'biz_domains_synced': 0,
            'product_lines_synced': 0,
            'errors': 0
        }
    
    def connect_to_database(self):
        """连接到数据库"""
        try:
            connection = pymysql.connect(
                host=self.db_config['host'],
                port=int(self.db_config['port']),
                user=self.db_config['username'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info("成功连接到数据库")
            return connection
        except Exception as e:
            logger.error(f"连接数据库失败: {e}")
            return None

    def connect_to_bd_database(self):
        """连接到业务域数据源数据库"""
        if not self.bd_db_config:
            logger.warning("业务域数据源配置未提供，跳过业务域同步")
            return None

        try:
            connection = pymysql.connect(
                host=self.bd_db_config['host'],
                port=int(self.bd_db_config['port']),
                user=self.bd_db_config['username'],
                password=self.bd_db_config['password'],
                database=self.bd_db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info("成功连接到业务域数据源数据库")
            return connection
        except Exception as e:
            logger.error(f"连接业务域数据源数据库失败: {e}")
            return None

    def sync_biz_domains_and_product_lines(self, target_connection):
        """同步业务域和产品线数据"""
        if not self.bd_db_config:
            logger.info("跳过业务域和产品线同步（未配置数据源）")
            return

        bd_connection = self.connect_to_bd_database()
        if not bd_connection:
            logger.error("无法连接到业务域数据源，跳过业务域同步")
            return

        try:
            # 同步业务域
            self.sync_biz_domains(bd_connection, target_connection)

            # 同步产品线
            self.sync_product_lines(bd_connection, target_connection)

            target_connection.commit()
            logger.info("业务域和产品线同步完成")

        except Exception as e:
            target_connection.rollback()
            error_msg = f"同步业务域和产品线失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
        finally:
            bd_connection.close()

    def sync_biz_domains(self, source_connection, target_connection):
        """同步业务域数据"""
        try:
            with source_connection.cursor() as cursor:
                # 获取所有业务域
                cursor.execute("""
                    SELECT id, name, owner, owner_id, dept_id, type, `desc`,
                           tech_owner, tech_owner_id, pbu_owner, pbu_owner_id,
                           listing_status, create_date, modify_date
                    FROM biz_domain
                    WHERE is_deleted = 0
                """)
                biz_domains = cursor.fetchall()

                current_time = datetime.now()

                with target_connection.cursor() as target_cursor:
                    for bd in biz_domains:
                        external_id, name, owner, owner_id, dept_id, domain_type, desc, \
                        tech_owner, tech_owner_id, pbu_owner, pbu_owner_id, \
                        listing_status, create_date, modify_date = bd

                        # 检查是否已存在
                        target_cursor.execute(
                            "SELECT id FROM rds_manager_bizdomain WHERE external_id = %s",
                            (external_id,)
                        )
                        existing = target_cursor.fetchone()

                        if existing:
                            # 更新
                            update_sql = """
                            UPDATE rds_manager_bizdomain SET
                                name = %s, description = %s, owner = %s, owner_id = %s,
                                tech_owner = %s, tech_owner_id = %s, pbu_owner = %s, pbu_owner_id = %s,
                                dept_id = %s, domain_type = %s, listing_status = %s, updated_at = %s
                            WHERE external_id = %s
                            """
                            target_cursor.execute(update_sql, (
                                name, desc, owner, owner_id,
                                tech_owner, tech_owner_id, pbu_owner, pbu_owner_id,
                                dept_id, domain_type, bool(listing_status), current_time,
                                external_id
                            ))
                        else:
                            # 创建
                            insert_sql = """
                            INSERT INTO rds_manager_bizdomain (
                                external_id, name, description, owner, owner_id,
                                tech_owner, tech_owner_id, pbu_owner, pbu_owner_id,
                                dept_id, domain_type, is_active, listing_status,
                                created_at, updated_at
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """
                            target_cursor.execute(insert_sql, (
                                external_id, name, desc, owner, owner_id,
                                tech_owner, tech_owner_id, pbu_owner, pbu_owner_id,
                                dept_id, domain_type, True, bool(listing_status),
                                current_time, current_time
                            ))

                        self.stats['biz_domains_synced'] += 1

                logger.info(f"同步了 {len(biz_domains)} 个业务域")

        except Exception as e:
            error_msg = f"同步业务域失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            raise

    def sync_product_lines(self, source_connection, target_connection):
        """同步产品线数据"""
        try:
            with source_connection.cursor() as cursor:
                # 获取所有产品线
                cursor.execute("""
                    SELECT id, biz_domain_id, name, owner, owner_id,
                           bug_online_owner, bug_online_owner_id, sr_expert, sr_expert_id,
                           product_line_level, listing_status, create_date, modify_date
                    FROM product_line
                    WHERE is_deleted = 0
                """)
                product_lines = cursor.fetchall()

                current_time = datetime.now()

                with target_connection.cursor() as target_cursor:
                    for pl in product_lines:
                        external_id, biz_domain_id, name, owner, owner_id, \
                        bug_online_owner, bug_online_owner_id, sr_expert, sr_expert_id, \
                        product_line_level, listing_status, create_date, modify_date = pl

                        # 检查是否已存在
                        target_cursor.execute(
                            "SELECT id FROM rds_manager_productline WHERE external_id = %s",
                            (external_id,)
                        )
                        existing = target_cursor.fetchone()

                        if existing:
                            # 更新
                            update_sql = """
                            UPDATE rds_manager_productline SET
                                biz_domain_id = %s, name = %s, owner = %s, owner_id = %s,
                                bug_online_owner = %s, bug_online_owner_id = %s,
                                sr_expert = %s, sr_expert_id = %s, product_line_level = %s,
                                listing_status = %s, updated_at = %s
                            WHERE external_id = %s
                            """
                            target_cursor.execute(update_sql, (
                                biz_domain_id, name, owner, owner_id,
                                bug_online_owner, bug_online_owner_id,
                                sr_expert, sr_expert_id, product_line_level,
                                bool(listing_status), current_time,
                                external_id
                            ))
                        else:
                            # 创建
                            insert_sql = """
                            INSERT INTO rds_manager_productline (
                                external_id, biz_domain_id, name, owner, owner_id,
                                bug_online_owner, bug_online_owner_id, sr_expert, sr_expert_id,
                                product_line_level, is_active, listing_status,
                                created_at, updated_at
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """
                            target_cursor.execute(insert_sql, (
                                external_id, biz_domain_id, name, owner, owner_id,
                                bug_online_owner, bug_online_owner_id, sr_expert, sr_expert_id,
                                product_line_level, True, bool(listing_status),
                                current_time, current_time
                            ))

                        self.stats['product_lines_synced'] += 1

                logger.info(f"同步了 {len(product_lines)} 个产品线")

        except Exception as e:
            error_msg = f"同步产品线失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            raise

    def fetch_applications(self, limit: int = 100, offset: int = 0) -> List[Dict]:
        """从API获取应用列表"""
        try:
            url = f"{self.api_base_url}/api/applist"
            params = {'limit': limit, 'offset': offset}
            
            logger.info(f"正在获取应用列表: limit={limit}, offset={offset}")
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            apps = data.get('list', [])
            total_count = data.get('count', 0)
            
            logger.info(f"获取到 {len(apps)} 个应用，总数: {total_count}")
            return apps, total_count
            
        except Exception as e:
            error_msg = f"获取应用列表失败: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            return [], 0
    
    def fetch_all_applications(self) -> List[Dict]:
        """获取所有应用"""
        all_apps = []
        limit = 100
        offset = 0
        
        while True:
            apps, total_count = self.fetch_applications(limit, offset)
            if not apps:
                break
            
            all_apps.extend(apps)
            offset += limit
            
            # 如果已经获取了所有应用，退出循环
            if len(all_apps) >= total_count:
                break
        
        self.stats['total_apps_fetched'] = len(all_apps)
        logger.info(f"共获取到 {len(all_apps)} 个应用")
        return all_apps
    

    
    def get_default_business_domain_id(self, connection) -> int:
        """获取默认业务域ID，如果不存在则创建"""
        try:
            with connection.cursor() as cursor:
                # 查找默认业务域
                cursor.execute("SELECT id FROM rds_manager_businessdomain WHERE name = %s", ("默认业务域",))
                result = cursor.fetchone()
                
                if result:
                    return result[0]
                
                # 创建默认业务域
                insert_sql = """
                INSERT INTO rds_manager_businessdomain (
                    name, description, created_at, updated_at
                ) VALUES (%s, %s, %s, %s)
                """
                current_time = datetime.now()
                cursor.execute(insert_sql, (
                    "默认业务域", "系统自动创建的默认业务域", current_time, current_time
                ))
                
                domain_id = cursor.lastrowid
                logger.info(f"创建默认业务域: ID={domain_id}")
                return domain_id
                
        except Exception as e:
            logger.error(f"获取默认业务域失败: {e}")
            return 1  # 返回默认值
    
    def map_app_type(self, api_app_type: str) -> str:
        """映射应用类型"""
        return self.app_type_mapping.get(api_app_type, 'other')
    


    def build_description(self, app_data: Dict) -> str:
        """构建应用描述，包含所有相关信息"""
        description_parts = []

        # 基础备注
        remark = app_data.get('remark', '').strip()
        if remark:
            description_parts.append(f"备注: {remark}")

        # 服务信息
        eureka_service_name = app_data.get('eurekaServiceName', '').strip()
        if eureka_service_name:
            description_parts.append(f"服务名: {eureka_service_name}")

        # POM路径
        pom_path = app_data.get('pomPath', '').strip()
        if pom_path:
            description_parts.append(f"POM路径: {pom_path}")

        # 角色信息
        roles = app_data.get('role', [])
        if roles:
            role_info = []
            for role in roles:
                role_type = role.get('role', '')
                nickname = role.get('nickName', '')
                email = role.get('mail', '')

                if role_type == 'PROJECT_PRINCIPAL':
                    role_info.append(f"项目负责人: {nickname} ({email})")
                elif role_type == 'DEVLOPER':
                    role_info.append(f"开发者: {nickname} ({email})")
                else:
                    role_info.append(f"{role_type}: {nickname} ({email})")

            if role_info:
                description_parts.append("角色信息: " + "; ".join(role_info))

        return " | ".join(description_parts) if description_parts else ""

    def sync_application(self, connection, app_data: Dict, default_domain_id: int):
        """同步单个应用到数据库"""
        try:
            api_id = app_data.get('id')
            name = app_data.get('name', '')
            code_path = app_data.get('codePath', '')
            app_type = app_data.get('appType', '')

            if not api_id or not name:
                logger.warning(f"应用数据不完整，跳过: {app_data}")
                self.stats['apps_skipped'] += 1
                return

            # 使用API的id作为应用代码
            app_code = str(api_id)

            # 构建完整描述
            description = self.build_description(app_data)

            current_time = datetime.now()

            with connection.cursor() as cursor:
                # 检查应用是否已存在（通过code）
                check_sql = "SELECT id FROM rds_manager_application WHERE code = %s"
                cursor.execute(check_sql, (app_code,))
                existing = cursor.fetchone()

                if existing:
                    # 更新现有应用
                    update_sql = """
                    UPDATE rds_manager_application SET
                        name = %s, description = %s, repository_url = %s,
                        app_type = %s, updated_at = %s
                    WHERE id = %s
                    """
                    cursor.execute(update_sql, (
                        name, description, code_path,
                        self.map_app_type(app_type), current_time,
                        existing[0]
                    ))
                    self.stats['apps_updated'] += 1
                    logger.info(f"更新应用: {name} (Code: {app_code})")
                else:
                    # 创建新应用
                    insert_sql = """
                    INSERT INTO rds_manager_application (
                        name, code, description, business_domain_id,
                        app_type, environment, repository_url,
                        is_active, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_sql, (
                        name, app_code, description, default_domain_id,
                        self.map_app_type(app_type), 'prod', code_path,
                        True, current_time, current_time
                    ))
                    app_id = cursor.lastrowid
                    self.stats['apps_created'] += 1
                    logger.info(f"创建新应用: {name} (Code: {app_code}, ID: {app_id})")

        except Exception as e:
            error_msg = f"同步应用失败 {app_data.get('name', 'Unknown')}: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
            self.stats['errors'] += 1

    def run_sync(self):
        """运行同步任务"""
        logger.info("开始应用系统同步")

        # 连接数据库
        connection = self.connect_to_database()
        if not connection:
            logger.error("无法连接到数据库，同步任务终止")
            return

        try:
            # 1. 先同步业务域和产品线数据
            self.sync_biz_domains_and_product_lines(connection)

            # 2. 获取默认业务域ID
            default_domain_id = self.get_default_business_domain_id(connection)

            # 3. 获取所有应用
            applications = self.fetch_all_applications()

            if not applications:
                logger.warning("未获取到任何应用数据")
                return

            # 4. 同步每个应用
            for app_data in applications:
                try:
                    self.sync_application(connection, app_data, default_domain_id)
                    connection.commit()  # 每个应用同步后提交
                except Exception as e:
                    connection.rollback()
                    logger.error(f"同步应用失败，回滚事务: {e}")
                    self.stats['errors'] += 1

            logger.info("应用同步任务完成")
            self.output_stats()

        except Exception as e:
            connection.rollback()
            error_msg = f"同步过程中出现严重错误: {e}"
            logger.error(error_msg)
            error_logger.error(error_msg)
        finally:
            connection.close()

    def output_stats(self):
        """输出统计信息"""
        logger.info("同步任务统计:")
        logger.info(f"- 业务域同步数: {self.stats['biz_domains_synced']}")
        logger.info(f"- 产品线同步数: {self.stats['product_lines_synced']}")
        logger.info(f"- 获取应用总数: {self.stats['total_apps_fetched']}")
        logger.info(f"- 创建应用数: {self.stats['apps_created']}")
        logger.info(f"- 更新应用数: {self.stats['apps_updated']}")
        logger.info(f"- 跳过应用数: {self.stats['apps_skipped']}")
        logger.info(f"- 错误数量: {self.stats['errors']}")

        # 输出到错误日志
        stats_summary = (
            f"应用同步完成: "
            f"业务域={self.stats['biz_domains_synced']}, "
            f"产品线={self.stats['product_lines_synced']}, "
            f"获取应用={self.stats['total_apps_fetched']}, "
            f"创建应用={self.stats['apps_created']}, "
            f"更新应用={self.stats['apps_updated']}, "
            f"跳过应用={self.stats['apps_skipped']}, "
            f"错误={self.stats['errors']}"
        )
        error_logger.error(stats_summary)


def load_config(config_file: str = None) -> Dict:
    """加载配置文件"""
    script_dir = os.path.dirname(os.path.abspath(__file__))

    if config_file is None:
        config_file = os.path.join(script_dir, 'config.ini')
    elif not os.path.isabs(config_file):
        config_file = os.path.join(script_dir, config_file)

    config = configparser.ConfigParser()
    try:
        logger.info(f"尝试加载配置文件: {config_file}")
        if not os.path.exists(config_file):
            logger.warning(f"配置文件不存在: {config_file}")
            return {}

        config.read(config_file, encoding='utf-8')
        logger.info(f"成功加载配置文件: {config_file}")

        result = {}

        # API配置
        if config.has_section('poseidon_api'):
            result['api_base_url'] = config.get('poseidon_api', 'base_url')
        else:
            logger.error("配置文件缺少 [poseidon_api] 配置段")
            result['api_base_url'] = None

        # 数据库配置
        if config.has_section('result_database'):
            result['db_config'] = {
                'host': config.get('result_database', 'host'),
                'port': config.getint('result_database', 'port', fallback=3306),
                'username': config.get('result_database', 'username'),
                'password': config.get('result_database', 'password'),
                'database': config.get('result_database', 'database')
            }
        else:
            logger.error("配置文件缺少 [result_database] 配置段")
            result['db_config'] = None

        # 业务域数据源配置
        if config.has_section('bd_database'):
            result['bd_db_config'] = {
                'host': config.get('bd_database', 'host'),
                'port': config.getint('bd_database', 'port', fallback=3306),
                'username': config.get('bd_database', 'username'),
                'password': config.get('bd_database', 'password'),
                'database': config.get('bd_database', 'database')
            }
        else:
            logger.warning("配置文件缺少 [bd_database] 配置段，将跳过业务域同步")
            result['bd_db_config'] = None

        return result
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return {}


def main():
    """主函数"""
    print("应用系统同步脚本")
    print("=" * 50)

    # 加载配置
    config = load_config()

    if not config:
        print("配置文件加载失败，请手动输入配置")
        api_base_url = input("请输入Poseidon API基础URL: ").strip()

        print("\n请输入数据库配置:")
        db_config = {
            'host': input("数据库主机: ").strip(),
            'port': int(input("数据库端口 (默认: 3306): ").strip() or "3306"),
            'username': input("数据库用户名: ").strip(),
            'password': input("数据库密码: ").strip(),
            'database': input("数据库名: ").strip()
        }
    else:
        api_base_url = config.get('api_base_url')
        db_config = config.get('db_config')
        bd_db_config = config.get('bd_db_config')

    if not api_base_url:
        print("错误: API基础URL未配置")
        return

    if not db_config:
        print("错误: 数据库配置不完整")
        return

    # 创建同步器
    sync = ApplicationSync(api_base_url, db_config, bd_db_config)

    try:
        # 运行同步
        sync.run_sync()

        print(f"\n同步任务完成!")
        print(f"- 业务域同步数: {sync.stats['biz_domains_synced']}")
        print(f"- 产品线同步数: {sync.stats['product_lines_synced']}")
        print(f"- 获取应用总数: {sync.stats['total_apps_fetched']}")
        print(f"- 创建应用数: {sync.stats['apps_created']}")
        print(f"- 更新应用数: {sync.stats['apps_updated']}")
        print(f"- 跳过应用数: {sync.stats['apps_skipped']}")
        print(f"- 错误数量: {sync.stats['errors']}")

    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"同步过程中出现错误: {e}")


if __name__ == "__main__":
    main()
