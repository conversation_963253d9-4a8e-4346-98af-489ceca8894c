# 用户自动同步API文档

## 概述

本文档描述了用户自动同步API的使用方法，该API为后续与公司用户中心的自动同步机制预留。

## API接口

### 1. 用户同步接口

**接口地址：** `POST /accounts/api/users/sync/`

**权限要求：** 超级管理员

**请求格式：**
```json
{
    "users": [
        {
            "username": "zhang<PERSON>",
            "real_name": "张三",
            "nickname": "小张",
            "nickname_pinyin": "zhangsan",
            "email": "<EMAIL>",
            "phone": "***********",
            "department": "技术部",
            "manager_id": null,
            "is_active": true
        }
    ]
}
```

**字段说明：**
- `username`: 用户名（必填）
- `real_name`: 真实姓名
- `nickname`: 花名
- `nickname_pinyin`: 花名拼音（与username保持一致）
- `email`: 邮箱地址
- `phone`: 手机号码
- `department`: 部门
- `manager_id`: 直接上级的用户ID
- `is_active`: 账号状态（true=启用，false=禁用）

**响应格式：**
```json
{
    "success": true,
    "message": "用户同步完成",
    "stats": {
        "total_received": 3,
        "created": 2,
        "updated": 1,
        "skipped": 0,
        "errors": []
    }
}
```

### 2. 同步状态查询接口

**接口地址：** `GET /accounts/api/users/sync-status/`

**权限要求：** 管理员

**响应格式：**
```json
{
    "total_users": 25,
    "auto_sync_users": 15,
    "domain_login_users": 8,
    "manual_users": 2,
    "recent_synced_24h": 5,
    "recent_synced_week": 12,
    "incomplete_info_count": 3,
    "last_sync_time": "2025-08-04T10:30:00Z"
}
```

## 同步逻辑

### 用户创建/更新规则

1. **新用户创建：**
   - 如果用户名不存在，创建新用户
   - 设置 `sync_source` 为 `auto_sync`
   - 自动分配所有RDS实例的查看权限
   - 检查信息完整度

2. **现有用户更新：**
   - 比较字段值，只更新有变化的字段
   - 更新 `last_sync_time` 为当前时间
   - 重新检查信息完整度

3. **错误处理：**
   - 单个用户同步失败不影响其他用户
   - 记录详细的错误信息
   - 返回完整的统计信息

### 数据验证

- `username` 或 `nickname_pinyin` 必须提供其中之一
- 邮箱格式验证
- 手机号格式验证（可选）
- 部门名称长度限制

## 使用示例

### Python示例

```python
import requests
import json

# 同步用户数据
def sync_users(users_data, auth_token):
    url = 'http://your-domain.com/accounts/api/users/sync/'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {auth_token}'
    }
    
    payload = {'users': users_data}
    
    response = requests.post(url, 
                           data=json.dumps(payload), 
                           headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"同步成功: {result['stats']}")
    else:
        print(f"同步失败: {response.text}")

# 示例用户数据
users = [
    {
        'username': 'zhangsan',
        'real_name': '张三',
        'nickname': '小张',
        'email': '<EMAIL>',
        'department': '技术部'
    }
]

sync_users(users, 'your_auth_token')
```

### cURL示例

```bash
curl -X POST http://localhost:8000/accounts/api/users/sync/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "users": [
      {
        "username": "zhangsan",
        "real_name": "张三",
        "nickname": "小张",
        "email": "<EMAIL>",
        "department": "技术部"
      }
    ]
  }'
```

## 定时同步建议

### 1. 使用Celery定时任务

```python
from celery import shared_task
import requests

@shared_task
def sync_users_from_hr_system():
    """从HR系统同步用户信息"""
    # 1. 从HR系统获取用户数据
    hr_users = fetch_users_from_hr()
    
    # 2. 转换数据格式
    formatted_users = format_user_data(hr_users)
    
    # 3. 调用同步API
    sync_result = call_sync_api(formatted_users)
    
    return sync_result
```

### 2. 使用Django管理命令

```python
from django.core.management.base import BaseCommand
from accounts.models import User

class Command(BaseCommand):
    help = '从外部系统同步用户信息'
    
    def handle(self, *args, **options):
        # 同步逻辑
        pass
```

## 监控和日志

### 日志记录

系统会自动记录以下信息：
- 同步开始和结束时间
- 处理的用户数量
- 创建/更新/跳过的用户统计
- 错误详情和堆栈信息

### 监控指标

建议监控以下指标：
- 同步成功率
- 同步耗时
- 错误频率
- 数据完整性

## 安全考虑

1. **认证授权：** 只有超级管理员可以执行同步
2. **数据验证：** 严格验证输入数据格式
3. **事务处理：** 使用数据库事务确保数据一致性
4. **错误隔离：** 单个用户错误不影响整体同步
5. **日志审计：** 记录所有同步操作的详细日志

## 故障排除

### 常见错误

1. **权限不足：** 确保使用超级管理员账号
2. **数据格式错误：** 检查JSON格式和字段类型
3. **网络超时：** 适当增加请求超时时间
4. **数据库锁定：** 避免并发执行多个同步任务

### 调试建议

1. 先使用少量测试数据验证接口
2. 检查服务器日志获取详细错误信息
3. 使用同步状态接口监控同步结果
4. 定期备份用户数据
