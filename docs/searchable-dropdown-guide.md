# 通用可搜索下拉框使用规范

## 概述

本文档定义了项目中通用可搜索下拉框组件的使用规范，确保所有页面的搜索框具有统一的交互体验和视觉效果。

## 设计原则

1. **统一性**：所有搜索框使用相同的交互模式和视觉样式
2. **易用性**：支持键盘导航、搜索过滤、点击选择等多种交互方式
3. **响应式**：在不同屏幕尺寸下都能正常工作
4. **可访问性**：支持键盘操作和屏幕阅读器
5. **性能**：高效的搜索过滤和DOM操作

## 组件特性

### 核心功能
- ✅ 下拉展开/收起
- ✅ 实时搜索过滤
- ✅ 键盘导航（上下箭头、回车、ESC）
- ✅ 点击外部关闭
- ✅ 选中状态管理
- ✅ 自定义回调函数

### 高级功能
- ✅ 动态选项更新
- ✅ 禁用/启用状态
- ✅ 清除功能
- ✅ 加载状态
- ✅ 错误状态
- ✅ 分组显示
- ✅ 多选模式（可选）

## 使用方法

### 1. 引入资源

在模板中引入CSS和JS文件：

```html
{% load static %}

<!-- CSS -->
<link rel="stylesheet" href="{% static 'rds_manager/css/searchable-dropdown.css' %}">

<!-- JavaScript -->
<script src="{% static 'rds_manager/js/searchable-dropdown.js' %}"></script>
```

### 2. HTML结构

```html
<div class="searchable-dropdown" id="myDropdown">
    <div class="dropdown-input-container">
        <input type="text" id="mySearch" class="form-control dropdown-search" 
               placeholder="请选择..." readonly>
        <input type="hidden" id="mySelect" name="my_field" value="">
        <span class="dropdown-arrow">▼</span>
    </div>
    <div class="dropdown-menu-custom" id="myDropdownMenu">
        <div class="dropdown-search-container">
            <input type="text" class="form-control form-control-sm" 
                   placeholder="搜索..." id="mySearchInput">
        </div>
        <div class="dropdown-options" id="myOptions">
            <div class="dropdown-option" data-value="" data-text="全部">全部</div>
            <div class="dropdown-option" data-value="1" data-text="选项1">选项1</div>
            <div class="dropdown-option" data-value="2" data-text="选项2">选项2</div>
        </div>
    </div>
</div>
```

### 3. JavaScript初始化

```javascript
$(document).ready(function() {
    const dropdown = initializeSearchableDropdown({
        dropdownId: 'myDropdown',
        searchInputId: 'mySearch',
        hiddenInputId: 'mySelect',
        filterInputId: 'mySearchInput',
        optionsContainerId: 'myOptions',
        placeholder: '请选择...',
        searchPlaceholder: '搜索...',
        onChange: function(value, text, element) {
            console.log('选择变更:', value, text);
            // 自定义处理逻辑
        }
    });
});
```

## 配置选项

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `dropdownId` | string | ✅ | - | 下拉框容器ID |
| `searchInputId` | string | ✅ | - | 显示输入框ID |
| `hiddenInputId` | string | ✅ | - | 隐藏字段ID |
| `filterInputId` | string | ✅ | - | 搜索输入框ID |
| `optionsContainerId` | string | ✅ | - | 选项容器ID |
| `placeholder` | string | ❌ | '请选择...' | 显示输入框占位符 |
| `searchPlaceholder` | string | ❌ | '搜索...' | 搜索输入框占位符 |
| `allowClear` | boolean | ❌ | true | 是否允许清除 |
| `onChange` | function | ❌ | null | 选择变更回调 |

## API方法

### 实例方法

```javascript
const dropdown = initializeSearchableDropdown(config);

// 设置值
dropdown.setValue('1');

// 获取值
const value = dropdown.getValue();

// 获取显示文本
const text = dropdown.getText();

// 清除选择
dropdown.clear();

// 禁用/启用
dropdown.disable();
dropdown.enable();

// 动态更新选项
dropdown.updateOptions([
    { value: '1', text: '新选项1' },
    { value: '2', text: '新选项2' }
]);

// 添加选项
dropdown.addOption('3', '新选项3');

// 删除选项
dropdown.removeOption('1');
```

## 样式定制

### CSS变量

```css
:root {
    --dropdown-border-color: #ced4da;
    --dropdown-focus-color: #86b7fe;
    --dropdown-selected-bg: #0d6efd;
    --dropdown-hover-bg: #e9ecef;
    --dropdown-max-height: 240px;
}
```

### 状态样式

```css
/* 错误状态 */
.searchable-dropdown.error .dropdown-search {
    border-color: #dc3545;
}

/* 成功状态 */
.searchable-dropdown.success .dropdown-search {
    border-color: #28a745;
}

/* 禁用状态 */
.searchable-dropdown.disabled {
    opacity: 0.6;
    pointer-events: none;
}
```

## 最佳实践

### 1. 命名规范

- 容器ID：`{功能}Dropdown`（如：`appDropdown`、`instanceDropdown`）
- 搜索输入框：`{功能}Search`（如：`appSearch`、`instanceSearch`）
- 隐藏字段：`{功能}Select`或表单字段名（如：`app_id`、`instance_id`）
- 过滤输入框：`{功能}SearchInput`（如：`appSearchInput`）
- 选项容器：`{功能}Options`（如：`appOptions`）

### 2. 数据属性

```html
<div class="dropdown-option" 
     data-value="unique_id" 
     data-text="显示文本"
     data-group="分组名称">
    显示文本
</div>
```

### 3. 响应式设计

- 在移动端自动调整下拉框宽度
- 确保搜索框在小屏幕上可用
- 考虑触摸设备的交互体验

### 4. 性能优化

- 对于大量选项（>100），考虑虚拟滚动
- 使用防抖处理搜索输入
- 避免频繁的DOM操作

### 5. 可访问性

- 确保键盘导航正常工作
- 添加适当的ARIA属性
- 支持屏幕阅读器

## 常见问题

### Q: 在模态框中使用时z-index问题？
A: 添加CSS规则：
```css
.modal .searchable-dropdown .dropdown-menu-custom {
    z-index: 1060;
}
```

### Q: 如何处理异步加载的选项？
A: 使用`updateOptions`方法：
```javascript
// 显示加载状态
dropdown.showLoading();

// 异步获取数据
fetch('/api/options').then(response => {
    return response.json();
}).then(data => {
    dropdown.updateOptions(data);
    dropdown.hideLoading();
});
```

### Q: 如何实现级联选择？
A: 在onChange回调中更新下级选项：
```javascript
onChange: function(value, text) {
    // 更新下级下拉框选项
    updateChildDropdown(value);
}
```

## 版本历史

- v1.0 (2025-08-01): 初始版本，基于高风险SQL页面搜索框实现

## 维护团队

- 开发：DBA Team
- 文档：DBA Team
- 测试：QA Team
