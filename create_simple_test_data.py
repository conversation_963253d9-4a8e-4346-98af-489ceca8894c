#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dba_0331.settings')
django.setup()

from accounts.models import User
from rds_manager.models import BusinessDomain, Application, Database

def create_simple_data():
    """创建简单的测试数据"""
    print("创建简单测试数据...")
    
    # 创建用户
    user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': '管理员',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        user.set_password('admin123')
        user.save()
        print("创建管理员用户: admin")
    
    # 创建业务域
    domain, created = BusinessDomain.objects.get_or_create(
        code='ECOMMERCE',
        defaults={
            'name': '电商平台',
            'description': '电商相关的所有应用系统',
            'team_leader_id': user.id,
            'supervisor_id': user.id,
            'contact_email': '<EMAIL>',
            'is_active': True
        }
    )
    if created:
        print("创建业务域: 电商平台")
    
    # 创建应用
    app1, created = Application.objects.get_or_create(
        code='MALL_WEB',
        defaults={
            'name': '商城网站',
            'description': '电商平台的前端网站',
            'business_domain_id': domain.id,
            'app_type': 'web',
            'environment': 'prod',
            'version': '1.0.0',
            'tech_owner_id': user.id,
            'business_owner_id': user.id,
            'contact_email': '<EMAIL>',
            'is_active': True
        }
    )
    if created:
        print("创建应用: 商城网站")
    
    app2, created = Application.objects.get_or_create(
        code='MALL_API',
        defaults={
            'name': '商城API',
            'description': '电商平台的后端API服务',
            'business_domain_id': domain.id,
            'app_type': 'api',
            'environment': 'prod',
            'version': '2.1.0',
            'tech_owner_id': user.id,
            'business_owner_id': user.id,
            'contact_email': '<EMAIL>',
            'is_active': True
        }
    )
    if created:
        print("创建应用: 商城API")
    
    print("简单测试数据创建完成！")
    print(f"业务域数量: {BusinessDomain.objects.count()}")
    print(f"应用数量: {Application.objects.count()}")

if __name__ == '__main__':
    create_simple_data()
