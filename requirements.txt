Django==4.2
djangorestframework==3.16.0
aliyun-python-sdk-core==2.16.0
aliyun-python-sdk-rds==2.7.49
aliyun-python-sdk-das==2.0.36
cryptography==44.0.2
PyMySQL==1.1.0
python-dateutil==2.8.2
pytz==2023.3
Pillow==10.2.0
gunicorn==21.2.0
python-json-logger==2.0.7
loguru==0.7.2
# 如果在macOS上安装MySQL客户端遇到问题，可以尝试以下方式:
# brew install mysql-client
# export PATH="/usr/local/opt/mysql-client/bin:$PATH"
# export LDFLAGS="-L/usr/local/opt/mysql-client/lib"
# export CPPFLAGS="-I/usr/local/opt/mysql-client/include"
# pip install mysqlclient 