# 应用分类管理系统

## 概述

本系统为现有的DBA平台新增了应用分类管理功能，支持按业务域对应用进行分类管理，并建立应用与数据库的关联关系。

## 功能特性

### 1. 层级结构管理
- **总监层级**：最高管理层，可以管理多个业务域
- **业务域层级**：按业务领域划分，每个业务域有对应的团队负责人
- **应用层级**：具体的应用系统，归属于某个业务域
- **数据库层级**：与应用关联的数据库实例

### 2. 核心模型

#### BusinessDomain（业务域）
- 业务域名称和代码
- 团队负责人（team_leader）
- 总监（supervisor）
- 联系信息（邮箱、电话）
- 状态管理

#### Application（应用系统）
- 应用名称和代码
- 所属业务域
- 应用类型（Web应用、API服务、批处理、微服务等）
- 环境（开发、测试、预发布、生产）
- 技术负责人和业务负责人
- 版本信息和联系方式

#### Database（数据库分配）
- 数据库与应用的直接分配关系
- 通过Database模型的app_id字段关联应用
- 支持分配人和分配时间记录
- 简化的数据库管理方式

### 3. 管理界面

#### 应用管理仪表盘
- 路径：`/rds/applications/`
- 功能：统计概览、业务域分布、应用类型分析

#### 业务域管理
- 路径：`/rds/business-domains/`
- 功能：业务域列表、创建和编辑业务域

#### 应用系统管理
- 路径：`/rds/applications/list/`
- 功能：应用列表、筛选、详情查看

#### 数据库分配管理
- 路径：`/rds/database-assignment/`
- 功能：数据库分配管理、筛选和编辑

### 4. Admin后台管理
- 业务域管理：`/admin/rds_manager/businessdomain/`
- 应用管理：`/admin/rds_manager/application/`
- 数据库管理：`/admin/rds_manager/database/`

## 技术实现

### 1. 数据库设计
- **不使用外键**：所有关联都使用整数/字符串字段存储ID
- **索引优化**：为常用查询字段建立索引
- **唯一约束**：确保代码和关联关系的唯一性

### 2. 权限控制
- Admin用户可以管理所有应用和数据库关联
- 支持按负责人进行权限控制（预留功能）

### 3. 导航集成
- 在主导航栏中新增"应用管理"分组
- 包含应用仪表盘、业务域管理、应用系统、数据库分配等入口

## 使用指南

### 1. 创建业务域
1. 访问 `/admin/rds_manager/businessdomain/add/`
2. 填写业务域名称、代码、描述
3. 指定团队负责人和总监（可选）
4. 填写联系信息

### 2. 创建应用
1. 访问 `/admin/rds_manager/application/add/`
2. 填写应用基本信息
3. 选择所属业务域
4. 指定技术负责人和业务负责人
5. 设置应用类型和环境

### 3. 建立数据库关联
1. 访问 `/admin/rds_manager/applicationdatabase/add/`
2. 选择应用和数据库
3. 设置关联类型和访问权限
4. 配置优先级和备注信息

### 4. 查看和管理
- 通过应用仪表盘查看整体统计
- 使用筛选功能快速定位应用
- 在应用详情页面查看关联的数据库

## 数据迁移

系统已包含数据库迁移文件：
```bash
python manage.py migrate
```

## 测试数据

可以运行以下脚本创建测试数据：
```bash
python init_application_data.py
```

该脚本会创建：
- 4个业务域（电商平台、用户中心、数据分析、支付系统）
- 8个应用系统
- 多个应用数据库关联关系
- 测试用户和权限

## 扩展功能

### 已实现
- ✅ 业务域管理
- ✅ 应用系统管理
- ✅ 数据库关联管理
- ✅ Admin后台集成
- ✅ 前端界面
- ✅ 导航集成

### 预留功能（未实现）
- ⏸️ 应用数据同步（从外部系统）
- ⏸️ 细粒度权限控制
- ⏸️ 审批流程
- ⏸️ 应用监控集成
- ⏸️ API接口

## 注意事项

1. **数据一致性**：由于不使用外键，需要在应用层面保证数据一致性
2. **权限管理**：当前主要通过Admin后台进行管理，建议只给授权用户开放权限
3. **数据备份**：建议定期备份应用管理相关数据
4. **性能优化**：大量数据时可能需要进一步优化查询性能

## 技术支持

如有问题，请联系开发团队或查看相关文档。
