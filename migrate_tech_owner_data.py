#!/usr/bin/env python
"""
数据迁移脚本：将Application表中的负责人信息迁移到ProductLine表中
注意：这个脚本需要在执行数据库迁移之前运行
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dba_0331.settings')
django.setup()

from django.db import connection

def migrate_tech_owner_data():
    """迁移技术负责人数据"""
    print("=== 迁移技术负责人数据 ===")
    
    with connection.cursor() as cursor:
        # 首先检查Application表中是否还有tech_owner相关字段
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'rds_manager_application' 
            AND column_name IN ('tech_owner_id', 'tech_owner_name')
        """)
        existing_columns = [row[0] for row in cursor.fetchall()]
        
        if not existing_columns:
            print("Application表中已经没有tech_owner相关字段，跳过迁移")
            return
        
        print(f"找到字段: {existing_columns}")
        
        # 查询所有有产品线关联且有技术负责人的应用
        query = """
            SELECT DISTINCT 
                a.product_line_id,
                a.tech_owner_id,
                a.tech_owner_name
            FROM rds_manager_application a
            WHERE a.product_line_id IS NOT NULL 
            AND (a.tech_owner_id IS NOT NULL OR a.tech_owner_name IS NOT NULL)
        """
        
        cursor.execute(query)
        app_data = cursor.fetchall()
        
        print(f"找到 {len(app_data)} 个需要迁移的应用-产品线关联")
        
        # 按产品线分组，选择最合适的负责人
        product_line_owners = {}
        for product_line_id, tech_owner_id, tech_owner_name in app_data:
            if product_line_id not in product_line_owners:
                product_line_owners[product_line_id] = {
                    'tech_owner_id': tech_owner_id,
                    'tech_owner_name': tech_owner_name,
                    'count': 1
                }
            else:
                # 如果有多个应用指向同一个产品线，选择最常见的负责人
                # 这里简化处理，保持第一个遇到的负责人
                product_line_owners[product_line_id]['count'] += 1
        
        print(f"将为 {len(product_line_owners)} 个产品线设置负责人")
        
        # 更新ProductLine表
        updated_count = 0
        for product_line_id, owner_info in product_line_owners.items():
            # 检查ProductLine表是否有team_leader字段
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'rds_manager_productline' 
                AND column_name IN ('team_leader_id', 'team_leader_name')
            """)
            pl_columns = [row[0] for row in cursor.fetchall()]
            
            if not pl_columns:
                print("ProductLine表中还没有team_leader字段，请先执行数据库迁移")
                return
            
            # 更新ProductLine表
            update_query = """
                UPDATE rds_manager_productline 
                SET team_leader_id = %s, team_leader_name = %s
                WHERE id = %s
            """
            
            cursor.execute(update_query, [
                owner_info['tech_owner_id'],
                owner_info['tech_owner_name'],
                product_line_id
            ])
            
            updated_count += 1
            print(f"更新产品线 {product_line_id}: 负责人ID={owner_info['tech_owner_id']}, 姓名={owner_info['tech_owner_name']}")
        
        print(f"成功更新了 {updated_count} 个产品线的负责人信息")

def check_migration_status():
    """检查迁移状态"""
    print("=== 检查迁移状态 ===")
    
    with connection.cursor() as cursor:
        # 检查Application表字段
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'rds_manager_application' 
            AND column_name IN ('tech_owner_id', 'tech_owner_name')
        """)
        app_columns = [row[0] for row in cursor.fetchall()]
        print(f"Application表中的tech_owner字段: {app_columns}")
        
        # 检查ProductLine表字段
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'rds_manager_productline' 
            AND column_name IN ('team_leader_id', 'team_leader_name')
        """)
        pl_columns = [row[0] for row in cursor.fetchall()]
        print(f"ProductLine表中的team_leader字段: {pl_columns}")
        
        # 如果ProductLine表有team_leader字段，显示一些数据
        if pl_columns:
            cursor.execute("""
                SELECT id, name, team_leader_id, team_leader_name
                FROM rds_manager_productline 
                WHERE team_leader_id IS NOT NULL OR team_leader_name IS NOT NULL
                LIMIT 5
            """)
            pl_data = cursor.fetchall()
            print(f"ProductLine表中有负责人的记录数: {len(pl_data)}")
            for row in pl_data:
                print(f"  产品线 {row[0]} ({row[1]}): 负责人ID={row[2]}, 姓名={row[3]}")

if __name__ == "__main__":
    check_migration_status()
    
    response = input("\n是否要执行数据迁移？(y/n): ")
    if response.lower() == 'y':
        migrate_tech_owner_data()
        print("\n迁移完成，重新检查状态:")
        check_migration_status()
    else:
        print("跳过数据迁移")
