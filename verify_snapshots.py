#!/usr/bin/env python
"""
验证生成的快照数据
"""

import os
import django
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dba_0331.settings')
django.setup()

from rds_manager.models import DashboardDailySummary

def main():
    # 查询指定日期范围的数据
    start_date = '2025-07-01'
    end_date = '2025-08-05'
    
    snapshots = DashboardDailySummary.objects.filter(
        snapshot_date__range=[start_date, end_date]
    )
    
    total_records = snapshots.count()
    unique_dates = snapshots.values('snapshot_date').distinct().count()
    
    print(f"生成的快照记录总数: {total_records}")
    print(f"涵盖的日期数: {unique_dates}")
    print(f"预期日期数: 36 (2025-07-01 到 2025-08-05)")
    
    # 显示按日期分组的统计
    print("\n按日期分组的记录数:")
    date_counts = {}
    for snapshot in snapshots.values('snapshot_date').annotate(
        record_count=django.db.models.Count('id')
    ).order_by('snapshot_date'):
        date = snapshot['snapshot_date']
        count = snapshot['record_count']
        date_counts[date] = count
        print(f"  {date}: {count} 条记录")
    
    # 显示最新几条记录的详细信息
    print("\n最新几条记录详情:")
    latest_records = snapshots.order_by('-snapshot_date', 'biz_domain_name')[:10]
    for record in latest_records:
        print(f"  {record.snapshot_date} - {record.biz_domain_name} - {record.product_line_name} - "
              f"SQL执行数: {record.sql_execution_count:,}, 慢查询数: {record.slow_query_count:,}")
    
    # 统计汇总信息
    print("\n数据汇总统计:")
    total_sql_execution = snapshots.aggregate(
        total_sql=django.db.models.Sum('sql_execution_count'),
        total_slow_query=django.db.models.Sum('slow_query_count'),
        avg_risk_score=django.db.models.Avg('avg_risk_score')
    )
    
    print(f"  总SQL执行次数: {total_sql_execution['total_sql']:,}")
    print(f"  总慢查询次数: {total_sql_execution['total_slow_query']:,}")
    print(f"  平均风险评分: {total_sql_execution['avg_risk_score']:.2f}")
    
    # 检查数据完整性
    print("\n数据完整性检查:")
    expected_dates = 36
    if unique_dates == expected_dates:
        print("✓ 日期覆盖完整")
    else:
        print(f"✗ 日期覆盖不完整，预期 {expected_dates} 天，实际 {unique_dates} 天")
    
    if total_records > 0:
        print("✓ 成功生成快照数据")
    else:
        print("✗ 未找到快照数据")

if __name__ == '__main__':
    main()