# Generated by Django 4.2 on 2025-08-04 10:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='is_info_complete',
            field=models.BooleanField(default=False, help_text='用户信息是否已完整填写', verbose_name='信息完整'),
        ),
        migrations.AddField(
            model_name='user',
            name='last_sync_time',
            field=models.DateTimeField(blank=True, help_text='最后一次从用户中心同步的时间', null=True, verbose_name='最后同步时间'),
        ),
        migrations.AddField(
            model_name='user',
            name='manager_id',
            field=models.IntegerField(blank=True, help_text='直接上级的用户ID', null=True, verbose_name='上级ID'),
        ),
        migrations.AddField(
            model_name='user',
            name='nickname',
            field=models.CharField(blank=True, help_text='用户的花名', max_length=50, verbose_name='花名'),
        ),
        migrations.AddField(
            model_name='user',
            name='nickname_pinyin',
            field=models.CharField(blank=True, help_text='花名的拼音，与username保持一致', max_length=100, verbose_name='花名拼音'),
        ),
        migrations.AddField(
            model_name='user',
            name='real_name',
            field=models.CharField(blank=True, help_text='用户的真实姓名', max_length=50, verbose_name='真名'),
        ),
        migrations.AddField(
            model_name='user',
            name='sync_source',
            field=models.CharField(choices=[('manual', '手动创建'), ('domain_login', '域账号登录'), ('auto_sync', '自动同步')], default='domain_login', max_length=20, verbose_name='同步来源'),
        ),
    ]
