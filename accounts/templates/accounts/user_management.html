{% extends 'rds_manager/base.html' %}
{% load static %}

{% block title %}用户管理 - 阿里云RDS管理平台{% endblock %}

{% block extra_css %}
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.3.6/css/buttons.bootstrap5.min.css">
<style>
    .info-complete-badge {
        font-size: 0.75rem;
    }
    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #6610f2);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-right: 8px;
    }
    .editable-field {
        cursor: pointer;
        border-bottom: 1px dashed #dee2e6;
        padding: 2px 4px;
        border-radius: 3px;
        transition: background-color 0.2s;
    }
    .editable-field:hover {
        background-color: #f8f9fa;
    }
    .stats-card {
        border-left: 4px solid #007bff;
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-users me-2"></i>用户管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-secondary" id="refreshBtn">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-outline-primary" id="exportBtn">
                <i class="fas fa-download"></i> 导出
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4" id="statsCards">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">总用户数</h6>
                        <h3 class="mb-0" id="totalUsers">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">活跃用户</h6>
                        <h3 class="mb-0" id="activeUsers">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">信息不完整</h6>
                        <h3 class="mb-0" id="incompleteUsers">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title text-muted">域账号用户</h6>
                        <h3 class="mb-0" id="domainUsers">-</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-building fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选区域 -->
<div class="filter-section">
    <div class="row">
        <div class="col-md-3">
            <label for="departmentFilter" class="form-label">部门筛选</label>
            <select class="form-select" id="departmentFilter">
                <option value="">全部部门</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="roleFilter" class="form-label">角色筛选</label>
            <select class="form-select" id="roleFilter">
                <option value="">全部角色</option>
                <option value="admin">管理员</option>
                <option value="user">普通用户</option>
                <option value="readonly">只读用户</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="statusFilter" class="form-label">状态筛选</label>
            <select class="form-select" id="statusFilter">
                <option value="">全部状态</option>
                <option value="true">活跃</option>
                <option value="false">禁用</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="infoCompleteFilter" class="form-label">信息完整度</label>
            <select class="form-select" id="infoCompleteFilter">
                <option value="">全部</option>
                <option value="true">完整</option>
                <option value="false">不完整</option>
            </select>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">用户列表</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="usersTable">
                <thead class="table-light">
                    <tr>
                        <th>用户</th>
                        <th>真名</th>
                        <th>花名</th>
                        <th>邮箱</th>
                        <th>电话</th>
                        <th>部门</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>信息完整度</th>
                        <th>实例权限</th>
                        <th>最后登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 用户编辑模态框 -->
<div class="modal fade" id="userEditModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="userEditForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editRealName" class="form-label">真名</label>
                                <input type="text" class="form-control" id="editRealName" name="real_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editNickname" class="form-label">花名</label>
                                <input type="text" class="form-control" id="editNickname" name="nickname">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editEmail" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="editEmail" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPhone" class="form-label">电话</label>
                                <input type="text" class="form-control" id="editPhone" name="phone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editDepartment" class="form-label">部门</label>
                                <input type="text" class="form-control" id="editDepartment" name="department">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editRole" class="form-label">角色</label>
                                <select class="form-select" id="editRole" name="role">
                                    <option value="admin">管理员</option>
                                    <option value="user">普通用户</option>
                                    <option value="readonly">只读用户</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editManager" class="form-label">直接上级</label>
                                <select class="form-select" id="editManager" name="manager_id">
                                    <option value="">无</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="editIsActive" name="is_active">
                                    <label class="form-check-label" for="editIsActive">
                                        账号状态（启用/禁用）
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.html5.min.js"></script>

<script>
$(document).ready(function() {
    let usersTable;
    let currentEditUserId = null;

    // 初始化DataTable
    function initDataTable() {
        usersTable = $('#usersTable').DataTable({
            processing: true,
            serverSide: false,
            ajax: {
                url: '{% url "accounts:api_user_list" %}',
                type: 'GET',
                data: function(d) {
                    // 添加筛选参数
                    d.department = $('#departmentFilter').val();
                    d.role = $('#roleFilter').val();
                    d.is_active = $('#statusFilter').val();
                    d.is_info_complete = $('#infoCompleteFilter').val();
                },
                dataSrc: function(json) {
                    // 如果是分页数据，返回results
                    return json.results || json;
                }
            },
            columns: [
                {
                    data: null,
                    render: function(data, type, row) {
                        const avatar = `<div class="user-avatar">${row.display_name.charAt(0).toUpperCase()}</div>`;
                        return `${avatar}<span>${row.username}</span>`;
                    }
                },
                {
                    data: 'real_name',
                    render: function(data, type, row) {
                        return `<span class="editable-field" data-field="real_name" data-user-id="${row.id}">${data || '-'}</span>`;
                    }
                },
                {
                    data: 'nickname',
                    render: function(data, type, row) {
                        return `<span class="editable-field" data-field="nickname" data-user-id="${row.id}">${data || '-'}</span>`;
                    }
                },
                {
                    data: 'email',
                    render: function(data, type, row) {
                        return `<span class="editable-field" data-field="email" data-user-id="${row.id}">${data || '-'}</span>`;
                    }
                },
                {
                    data: 'phone',
                    render: function(data, type, row) {
                        return `<span class="editable-field" data-field="phone" data-user-id="${row.id}">${data || '-'}</span>`;
                    }
                },
                {
                    data: 'department',
                    render: function(data, type, row) {
                        return `<span class="editable-field" data-field="department" data-user-id="${row.id}">${data || '-'}</span>`;
                    }
                },
                {
                    data: 'role',
                    render: function(data, type, row) {
                        const roleMap = {
                            'admin': '<span class="badge bg-danger">管理员</span>',
                            'user': '<span class="badge bg-primary">普通用户</span>',
                            'readonly': '<span class="badge bg-secondary">只读用户</span>'
                        };
                        return roleMap[data] || data;
                    }
                },
                {
                    data: 'is_active',
                    render: function(data, type, row) {
                        return data ?
                            '<span class="badge bg-success">活跃</span>' :
                            '<span class="badge bg-danger">禁用</span>';
                    }
                },
                {
                    data: 'is_info_complete',
                    render: function(data, type, row) {
                        return data ?
                            '<span class="badge bg-success info-complete-badge">完整</span>' :
                            '<span class="badge bg-warning info-complete-badge">不完整</span>';
                    }
                },
                {
                    data: 'instance_count',
                    render: function(data, type, row) {
                        return `<span class="badge bg-info">${data || 0}</span>`;
                    }
                },
                {
                    data: 'last_login',
                    render: function(data, type, row) {
                        if (!data) return '-';
                        return new Date(data).toLocaleDateString('zh-CN');
                    }
                },
                {
                    data: null,
                    orderable: false,
                    render: function(data, type, row) {
                        return `
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-edit" data-user-id="${row.id}">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-permissions" data-user-id="${row.id}">
                                    <i class="fas fa-key"></i>
                                </button>
                            </div>
                        `;
                    }
                }
            ],
            order: [[10, 'desc']], // 按最后登录时间排序
            pageLength: 20,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/zh.json'
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> 导出Excel',
                    className: 'btn btn-success btn-sm'
                }
            ]
        });
    }

    // 加载统计数据
    function loadStats() {
        $.get('{% url "accounts:api_user_stats" %}', function(data) {
            $('#totalUsers').text(data.total_users);
            $('#activeUsers').text(data.active_users);
            $('#incompleteUsers').text(data.incomplete_info_users);
            $('#domainUsers').text(data.domain_login_users);

            // 填充部门筛选选项
            const departmentFilter = $('#departmentFilter');
            departmentFilter.empty().append('<option value="">全部部门</option>');
            data.departments.forEach(dept => {
                departmentFilter.append(`<option value="${dept}">${dept}</option>`);
            });
        }).fail(function() {
            console.error('加载统计数据失败');
        });
    }

    // 初始化
    initDataTable();
    loadStats();

    // 筛选器变化时重新加载数据
    $('#departmentFilter, #roleFilter, #statusFilter, #infoCompleteFilter').change(function() {
        usersTable.ajax.reload();
    });

    // 刷新按钮
    $('#refreshBtn').click(function() {
        usersTable.ajax.reload();
        loadStats();
    });

    // 编辑用户按钮点击事件
    $(document).on('click', '.btn-edit', function() {
        const userId = $(this).data('user-id');
        editUser(userId);
    });

    // 编辑用户函数
    function editUser(userId) {
        currentEditUserId = userId;

        // 获取用户详情
        $.get(`{% url "accounts:api_user_detail" user_id=0 %}`.replace('0', userId), function(data) {
            // 填充表单
            $('#editRealName').val(data.real_name || '');
            $('#editNickname').val(data.nickname || '');
            $('#editEmail').val(data.email || '');
            $('#editPhone').val(data.phone || '');
            $('#editDepartment').val(data.department || '');
            $('#editRole').val(data.role);
            $('#editIsActive').prop('checked', data.is_active);

            // 填充上级选项
            const managerSelect = $('#editManager');
            managerSelect.empty().append('<option value="">无</option>');
            if (data.manager_options) {
                data.manager_options.forEach(manager => {
                    const selected = manager.id === data.manager_id ? 'selected' : '';
                    managerSelect.append(`<option value="${manager.id}" ${selected}>${manager.name}</option>`);
                });
            }

            // 显示模态框
            $('#userEditModal').modal('show');
        }).fail(function() {
            alert('获取用户信息失败');
        });
    }

    // 保存用户信息
    $('#saveUserBtn').click(function() {
        const formData = {
            real_name: $('#editRealName').val(),
            nickname: $('#editNickname').val(),
            email: $('#editEmail').val(),
            phone: $('#editPhone').val(),
            department: $('#editDepartment').val(),
            role: $('#editRole').val(),
            manager_id: $('#editManager').val() || null,
            is_active: $('#editIsActive').prop('checked')
        };

        $.ajax({
            url: `{% url "accounts:api_user_detail" user_id=0 %}`.replace('0', currentEditUserId),
            type: 'PUT',
            data: JSON.stringify(formData),
            contentType: 'application/json',
            success: function(data) {
                $('#userEditModal').modal('hide');
                usersTable.ajax.reload();
                loadStats();
                alert('用户信息更新成功');
            },
            error: function(xhr) {
                const error = xhr.responseJSON;
                alert('更新失败: ' + (error.error || '未知错误'));
            }
        });
    });
});
</script>
{% endblock %}
