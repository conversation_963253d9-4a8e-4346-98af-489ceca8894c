from rest_framework import serializers
from django.contrib.auth import get_user_model
from rds_manager.models import InstanceAccessPermission, RDSInstance

User = get_user_model()


class UserListSerializer(serializers.ModelSerializer):
    """用户列表序列化器"""
    display_name = serializers.ReadOnlyField()
    manager_name = serializers.SerializerMethodField()
    instance_count = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'display_name', 'real_name', 'nickname', 
            'email', 'phone', 'department', 'role', 'is_active',
            'is_info_complete', 'sync_source', 'manager_name', 
            'instance_count', 'last_login', 'date_joined'
        ]
        read_only_fields = ['username', 'last_login', 'date_joined', 'sync_source']
    
    def get_manager_name(self, obj):
        """获取上级姓名"""
        manager = obj.manager
        return manager.display_name if manager else None
    
    def get_instance_count(self, obj):
        """获取用户有权限的实例数量"""
        return InstanceAccessPermission.objects.filter(user_id=obj.id).count()


class UserDetailSerializer(serializers.ModelSerializer):
    """用户详情序列化器"""
    display_name = serializers.ReadOnlyField()
    manager_name = serializers.SerializerMethodField()
    manager_options = serializers.SerializerMethodField()
    accessible_instances = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'display_name', 'real_name', 'nickname', 
            'nickname_pinyin', 'email', 'phone', 'department', 'role', 
            'is_active', 'is_info_complete', 'sync_source', 'manager_id',
            'manager_name', 'manager_options', 'accessible_instances',
            'last_login', 'date_joined', 'last_sync_time'
        ]
        read_only_fields = [
            'username', 'last_login', 'date_joined', 'sync_source', 
            'last_sync_time', 'is_info_complete'
        ]
    
    def get_manager_name(self, obj):
        """获取上级姓名"""
        manager = obj.manager
        return manager.display_name if manager else None
    
    def get_manager_options(self, obj):
        """获取可选的上级列表"""
        # 排除自己，避免循环引用
        managers = User.objects.exclude(id=obj.id).filter(
            role__in=['admin', 'user']
        ).values('id', 'username', 'real_name', 'nickname')
        
        return [
            {
                'id': m['id'],
                'name': m['real_name'] or m['nickname'] or m['username']
            }
            for m in managers
        ]
    
    def get_accessible_instances(self, obj):
        """获取用户可访问的实例列表"""
        permissions = InstanceAccessPermission.objects.filter(
            user_id=obj.id
        ).select_related()
        
        instances = []
        for perm in permissions:
            try:
                instance = RDSInstance.objects.get(instance_id=perm.instance_string_id)
                instances.append({
                    'instance_id': instance.instance_id,
                    'instance_name': instance.instance_name,
                    'engine': instance.engine,
                    'status': instance.status
                })
            except RDSInstance.DoesNotExist:
                continue
        
        return instances
    
    def update(self, instance, validated_data):
        """更新用户信息"""
        # 更新基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()
        
        # 更新后检查信息完整度
        instance.check_info_completeness()
        
        return instance


class UserBatchUpdateSerializer(serializers.Serializer):
    """批量更新用户序列化器"""
    user_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="要更新的用户ID列表"
    )
    department = serializers.CharField(max_length=100, required=False)
    role = serializers.ChoiceField(choices=User.ROLE_CHOICES, required=False)
    is_active = serializers.BooleanField(required=False)
    manager_id = serializers.IntegerField(required=False, allow_null=True)
    
    def validate_user_ids(self, value):
        """验证用户ID列表"""
        if not value:
            raise serializers.ValidationError("用户ID列表不能为空")
        
        # 检查用户是否存在
        existing_users = User.objects.filter(id__in=value).count()
        if existing_users != len(value):
            raise serializers.ValidationError("部分用户ID不存在")
        
        return value
    
    def validate_manager_id(self, value):
        """验证上级ID"""
        if value is not None:
            try:
                User.objects.get(id=value)
            except User.DoesNotExist:
                raise serializers.ValidationError("指定的上级用户不存在")
        return value


class InstancePermissionSerializer(serializers.Serializer):
    """实例权限序列化器"""
    user_id = serializers.IntegerField()
    instance_ids = serializers.ListField(
        child=serializers.CharField(),
        help_text="实例ID列表"
    )
    action = serializers.ChoiceField(
        choices=['grant', 'revoke'],
        help_text="操作类型：grant-授权，revoke-撤销"
    )
    
    def validate_user_id(self, value):
        """验证用户ID"""
        try:
            User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("用户不存在")
        return value
    
    def validate_instance_ids(self, value):
        """验证实例ID列表"""
        if not value:
            raise serializers.ValidationError("实例ID列表不能为空")
        
        # 检查实例是否存在
        existing_instances = RDSInstance.objects.filter(instance_id__in=value).count()
        if existing_instances != len(value):
            raise serializers.ValidationError("部分实例ID不存在")
        
        return value


class UserStatsSerializer(serializers.Serializer):
    """用户统计信息序列化器"""
    total_users = serializers.IntegerField()
    active_users = serializers.IntegerField()
    incomplete_info_users = serializers.IntegerField()
    domain_login_users = serializers.IntegerField()
    auto_sync_users = serializers.IntegerField()
    departments = serializers.ListField(child=serializers.CharField())
    roles_distribution = serializers.DictField()
