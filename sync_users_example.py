#!/usr/bin/env python
"""
用户同步示例脚本
演示如何使用用户同步API接口
"""

import requests
import json
from datetime import datetime

# 配置
API_BASE_URL = 'http://localhost:8000'
SYNC_API_URL = f'{API_BASE_URL}/accounts/api/users/sync/'
STATUS_API_URL = f'{API_BASE_URL}/accounts/api/users/sync-status/'

# 示例用户数据（模拟从外部系统获取的数据）
SAMPLE_USERS = [
    {
        'username': 'zhang<PERSON>',
        'real_name': '张三',
        'nickname': '小张',
        'nickname_pinyin': 'zhang<PERSON>',
        'email': 'zhang<PERSON>@tsign.cn',
        'phone': '***********',
        'department': '技术部',
        'manager_id': None,
        'is_active': True
    },
    {
        'username': 'lisi',
        'real_name': '李四',
        'nickname': '小李',
        'nickname_pinyin': 'lisi',
        'email': '<EMAIL>',
        'phone': '***********',
        'department': '产品部',
        'manager_id': None,
        'is_active': True
    },
    {
        'username': 'wangwu',
        'real_name': '王五',
        'nickname': '小王',
        'nickname_pinyin': 'wangwu',
        'email': '<EMAIL>',
        'phone': '***********',
        'department': '技术部',
        'manager_id': None,
        'is_active': True
    }
]


class UserSyncClient:
    """用户同步客户端"""
    
    def __init__(self, base_url, username=None, password=None):
        self.base_url = base_url
        self.session = requests.Session()
        
        # 如果提供了认证信息，进行登录
        if username and password:
            self.login(username, password)
    
    def login(self, username, password):
        """登录获取认证"""
        login_url = f'{self.base_url}/accounts/login/'
        
        # 先获取CSRF token
        response = self.session.get(login_url)
        if response.status_code == 200:
            # 从响应中提取CSRF token（简化处理）
            csrf_token = self.session.cookies.get('csrftoken')
            
            # 执行登录
            login_data = {
                'username': username,
                'password': password,
                'csrfmiddlewaretoken': csrf_token
            }
            
            response = self.session.post(login_url, data=login_data)
            if response.status_code == 302:  # 重定向表示登录成功
                print(f"登录成功: {username}")
                return True
            else:
                print(f"登录失败: {response.status_code}")
                return False
        else:
            print(f"获取登录页面失败: {response.status_code}")
            return False
    
    def sync_users(self, users_data):
        """同步用户数据"""
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': self.session.cookies.get('csrftoken', '')
        }
        
        payload = {
            'users': users_data
        }
        
        try:
            response = self.session.post(
                SYNC_API_URL, 
                data=json.dumps(payload),
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                print("同步成功!")
                print(f"统计信息: {json.dumps(result['stats'], indent=2, ensure_ascii=False)}")
                return result
            else:
                print(f"同步失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return None
                
        except Exception as e:
            print(f"同步过程中发生错误: {e}")
            return None
    
    def get_sync_status(self):
        """获取同步状态"""
        try:
            response = self.session.get(STATUS_API_URL)
            
            if response.status_code == 200:
                status_data = response.json()
                print("同步状态:")
                print(f"总用户数: {status_data['total_users']}")
                print(f"自动同步用户: {status_data['auto_sync_users']}")
                print(f"域账号用户: {status_data['domain_login_users']}")
                print(f"手动创建用户: {status_data['manual_users']}")
                print(f"24小时内同步: {status_data['recent_synced_24h']}")
                print(f"一周内同步: {status_data['recent_synced_week']}")
                print(f"信息不完整用户: {status_data['incomplete_info_count']}")
                print(f"最后同步时间: {status_data['last_sync_time']}")
                return status_data
            else:
                print(f"获取状态失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"获取状态时发生错误: {e}")
            return None


def main():
    """主函数"""
    print("=== 用户同步示例脚本 ===")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 创建同步客户端
    client = UserSyncClient(API_BASE_URL)
    
    # 注意：实际使用时需要提供有效的超级管理员账号
    # client.login('admin_username', 'admin_password')
    
    print("1. 获取当前同步状态:")
    client.get_sync_status()
    print()
    
    print("2. 执行用户同步:")
    print(f"准备同步 {len(SAMPLE_USERS)} 个用户...")
    
    # 执行同步（注意：需要先登录）
    # result = client.sync_users(SAMPLE_USERS)
    print("注意：请先取消注释登录代码并提供有效的管理员账号")
    print()
    
    print("3. 同步后状态:")
    # client.get_sync_status()
    
    print("=== 脚本执行完成 ===")


if __name__ == '__main__':
    main()
