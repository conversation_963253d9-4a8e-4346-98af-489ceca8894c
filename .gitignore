# Virtual environment
.venv/
venv/
env/

# Test files
test_*.py
*_test.py
test*.html

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# Django
*.log
db.sqlite3
db.sqlite3-journal

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Celery
celerybeat-schedule.db

# Config files with sensitive data
config.ini
*.ini
!config.ini.example
!config_standalone.ini.example

# Temporary files
temp_files/
*.tmp

# Logs
logs/
*.log

# Static files
staticfiles/