#!/usr/bin/env python
"""
批量生成数据库运行情况快照
用于聚合指定日期范围内的数据

使用方法:
python generate_dashboard_snapshots.py --start-date 2025-07-01 --end-date 2025-08-05
"""

import os
import sys
import django
import argparse
from datetime import datetime, timedelta
import logging

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dba_0331.settings')
django.setup()

from rds_manager.services import DashboardSnapshotService
from django.utils import timezone

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('dashboard_snapshots.log')
    ]
)
logger = logging.getLogger(__name__)

def parse_date(date_str):
    """解析日期字符串"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        raise argparse.ArgumentTypeError(f"无效的日期格式: {date_str}，请使用YYYY-MM-DD格式")

def generate_date_range(start_date, end_date):
    """生成日期范围"""
    current_date = start_date
    while current_date <= end_date:
        yield current_date
        current_date += timedelta(days=1)

def main():
    parser = argparse.ArgumentParser(description='批量生成数据库运行情况快照')
    parser.add_argument('--start-date', type=parse_date, required=True,
                       help='开始日期 (YYYY-MM-DD格式)')
    parser.add_argument('--end-date', type=parse_date, required=True,
                       help='结束日期 (YYYY-MM-DD格式)')
    parser.add_argument('--force', action='store_true',
                       help='强制重新生成已存在的快照')
    
    args = parser.parse_args()
    
    # 验证日期范围
    if args.start_date > args.end_date:
        logger.error("开始日期不能晚于结束日期")
        sys.exit(1)
    
    # 计算总天数
    total_days = (args.end_date - args.start_date).days + 1
    logger.info(f"开始生成快照数据，日期范围: {args.start_date} 到 {args.end_date}，共 {total_days} 天")
    
    # 初始化快照服务
    snapshot_service = DashboardSnapshotService()
    
    # 统计信息
    success_count = 0
    failed_count = 0
    failed_dates = []
    
    # 逐日生成快照
    for i, snapshot_date in enumerate(generate_date_range(args.start_date, args.end_date), 1):
        logger.info(f"[{i}/{total_days}] 正在生成 {snapshot_date} 的快照数据...")
        
        try:
            result = snapshot_service.generate_daily_snapshot(snapshot_date)
            
            if result['success']:
                success_count += 1
                data = result['data']
                logger.info(f"✓ {snapshot_date} 快照生成成功 - "
                          f"处理业务域: {data.get('processed_domains', 0)}, "
                          f"生成记录: {data.get('processed_records', 0)}, "
                          f"耗时: {data.get('execution_time', '未知')}")
            else:
                failed_count += 1
                failed_dates.append(snapshot_date)
                logger.error(f"✗ {snapshot_date} 快照生成失败: {result['message']}")
                
        except Exception as e:
            failed_count += 1
            failed_dates.append(snapshot_date)
            logger.error(f"✗ {snapshot_date} 快照生成异常: {str(e)}")
    
    # 输出汇总信息
    logger.info("\n" + "="*60)
    logger.info("快照生成完成汇总:")
    logger.info(f"总计天数: {total_days}")
    logger.info(f"成功生成: {success_count}")
    logger.info(f"生成失败: {failed_count}")
    
    if failed_dates:
        logger.info("失败日期列表:")
        for failed_date in failed_dates:
            logger.info(f"  - {failed_date}")
    
    logger.info("="*60)
    
    # 返回退出码
    if failed_count > 0:
        logger.warning(f"有 {failed_count} 个日期的快照生成失败，请检查日志")
        sys.exit(1)
    else:
        logger.info("所有快照生成成功！")
        sys.exit(0)

if __name__ == '__main__':
    main()