#!/usr/bin/env python
"""
检查和修复产品线数据一致性的脚本
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dba_0331.settings')
django.setup()

from rds_manager.models import ProductLine, Application

def check_product_line_data():
    """检查产品线数据一致性"""
    print("=== 产品线数据检查 ===")
    
    # 检查产品线数据
    product_lines = ProductLine.objects.all()
    print(f"总共有 {product_lines.count()} 个产品线")
    
    print("\n产品线列表:")
    for pl in product_lines[:10]:  # 只显示前10个
        print(f"  ID: {pl.id}, External ID: {pl.external_id}, Name: {pl.name}")
    
    # 检查应用的产品线关联
    apps_with_product_line = Application.objects.filter(product_line_id__isnull=False)
    print(f"\n有 {apps_with_product_line.count()} 个应用关联了产品线")
    
    print("\n应用产品线关联:")
    inconsistent_count = 0
    for app in apps_with_product_line[:10]:  # 只显示前10个
        try:
            # 尝试通过ID查找产品线
            product_line = ProductLine.objects.get(id=app.product_line_id)
            print(f"  ✓ App: {app.name} -> Product Line: {product_line.name} (ID: {app.product_line_id})")
        except ProductLine.DoesNotExist:
            # 尝试通过external_id查找
            try:
                product_line = ProductLine.objects.get(external_id=app.product_line_id)
                print(f"  ⚠ App: {app.name} -> Product Line: {product_line.name} (External ID: {app.product_line_id}, 需要修复为ID: {product_line.id})")
                inconsistent_count += 1
            except ProductLine.DoesNotExist:
                print(f"  ✗ App: {app.name} -> 产品线不存在 (ID: {app.product_line_id})")
                inconsistent_count += 1
    
    print(f"\n发现 {inconsistent_count} 个不一致的关联")
    return inconsistent_count

def fix_product_line_data():
    """修复产品线数据一致性"""
    print("\n=== 修复产品线数据 ===")
    
    apps_with_product_line = Application.objects.filter(product_line_id__isnull=False)
    fixed_count = 0
    
    for app in apps_with_product_line:
        try:
            # 尝试通过ID查找产品线
            ProductLine.objects.get(id=app.product_line_id)
            # 如果找到了，说明数据是正确的
            continue
        except ProductLine.DoesNotExist:
            # 尝试通过external_id查找
            try:
                product_line = ProductLine.objects.get(external_id=app.product_line_id)
                # 修复数据：将external_id改为id
                old_value = app.product_line_id
                app.product_line_id = product_line.id
                app.save()
                print(f"  修复 App: {app.name} 产品线ID: {old_value} -> {product_line.id}")
                fixed_count += 1
            except ProductLine.DoesNotExist:
                print(f"  无法修复 App: {app.name} 产品线ID: {app.product_line_id} (产品线不存在)")
    
    print(f"\n修复了 {fixed_count} 个应用的产品线关联")

if __name__ == "__main__":
    inconsistent_count = check_product_line_data()
    
    if inconsistent_count > 0:
        response = input(f"\n发现 {inconsistent_count} 个不一致的关联，是否要修复？(y/n): ")
        if response.lower() == 'y':
            fix_product_line_data()
            print("\n重新检查数据:")
            check_product_line_data()
        else:
            print("跳过修复")
    else:
        print("\n数据一致性检查通过！")
